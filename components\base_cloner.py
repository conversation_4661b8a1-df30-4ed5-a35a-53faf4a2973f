"""
Base Cloner Class для ClonerPro
Базовый класс для всех клонеров с общей функциональностью
"""

import bpy
from .base import BaseComponent


class BaseCloner(BaseComponent):
    """
    Базовый класс для всех клонеров ClonerPro
    
    Предоставляет общую функциональность:
    - Создание базовых сокетов клонера
    - Применение трансформаций (позиция, поворот, масштаб)
    - Рандомизация
    - Поддержка разных режимов (OBJECT, STACKED, COLLECTION)
    """
    
    component_type = "CLONER"
    
    def __init__(self):
        super().__init__()
    
    def get_base_cloner_sockets(self):
        """
        Базовые сокеты, общие для всех клонеров
        
        Returns:
            list: Список определений сокетов (name, socket_type, in_out, default)
        """
        return [
            # Geometry sockets
            ("Geometry", "NodeSocketGeometry", "INPUT", None),
            ("Instance Source", "NodeSocketObject", "INPUT", None),
            ("Collection", "NodeSocketCollection", "INPUT", None),
            ("Geometry", "NodeSocketGeometry", "OUTPUT", None),
            
            # Transform sockets
            ("Instance Scale", "NodeSocketVector", "INPUT", (1.0, 1.0, 1.0)),
            ("Instance Rotation", "NodeSocketVector", "INPUT", (0.0, 0.0, 0.0)),
            
            # Randomization sockets
            ("Random Position", "NodeSocketVector", "INPUT", (0.0, 0.0, 0.0)),
            ("Random Rotation", "NodeSocketVector", "INPUT", (0.0, 0.0, 0.0)),
            ("Random Scale", "NodeSocketFloat", "INPUT", 0.0),
            ("Random Seed", "NodeSocketInt", "INPUT", 0),
            
            # Global Transform sockets
            ("Global Position", "NodeSocketVector", "INPUT", (0.0, 0.0, 0.0)),
            ("Global Rotation", "NodeSocketVector", "INPUT", (0.0, 0.0, 0.0)),
            
            # Anti-recursion socket - значение будет установлено динамически
            ("Realize Instances", "NodeSocketBool", "INPUT", None),
        ]
    
    def create_cloner_interface(self, node_group, additional_sockets=None):
        """
        Создание полного интерфейса клонера (базовые + специфичные сокеты)
        
        Args:
            node_group: Node group для создания интерфейса
            additional_sockets: Дополнительные специфичные сокеты клонера
            
        Returns:
            dict: Маппинг всех созданных сокетов
        """
        interface = node_group.interface
        socket_mapping = {}
        
        # Создаем базовые сокеты
        base_sockets = self.get_base_cloner_sockets()
        for socket_name, socket_type, in_out, default_value in base_sockets:
            socket = interface.new_socket(socket_name, in_out=in_out, socket_type=socket_type)
            socket_mapping[socket_name.lower().replace(" ", "_")] = socket.identifier
            
            # Устанавливаем значения по умолчанию
            if in_out == "INPUT":
                try:
                    if socket_name == "Realize Instances":
                        # Для анти-рекурсии берем значение из глобальных настроек
                        from ..core.system.settings import get_behavior_settings
                        behavior = get_behavior_settings()
                        socket.default_value = behavior['anti_recursion']
                    elif default_value is not None:
                        socket.default_value = default_value
                except:
                    pass  # Некоторые типы сокетов не поддерживают default_value
        
        # Добавляем специфичные сокеты клонера
        if additional_sockets:
            for socket_name, socket_type, in_out, default_value in additional_sockets:
                socket = interface.new_socket(socket_name, in_out=in_out, socket_type=socket_type)
                socket_mapping[socket_name.lower().replace(" ", "_")] = socket.identifier
                
                if default_value is not None and in_out == "INPUT":
                    try:
                        socket.default_value = default_value
                    except:
                        pass
        
        return socket_mapping
    
    def create_base_nodes(self, node_group, mode="OBJECT"):
        """
        Создание базовых нодов, общих для всех клонеров
        
        Args:
            node_group: Node group
            mode: Режим клонера (OBJECT, STACKED, COLLECTION)
            
        Returns:
            dict: Словарь с созданными нодами
        """
        nodes = node_group.nodes
        links = node_group.links
        
        # Очищаем существующие ноды
        nodes.clear()
        
        # Группа input/output
        group_input = nodes.new('NodeGroupInput')
        group_input.location = (-800, 0)
        
        group_output = nodes.new('NodeGroupOutput')
        group_output.location = (800, 0)
        
        base_nodes = {
            'group_input': group_input,
            'group_output': group_output,
            'nodes': nodes,
            'links': links
        }
        
        return base_nodes
    
    def get_geometry_input(self, base_nodes, mode="OBJECT"):
        """
        Получить правильный geometry input в зависимости от режима
        
        Args:
            base_nodes: Словарь с базовыми нодами
            mode: Режим клонера
            
        Returns:
            NodeSocket: Выходной сокет геометрии для использования
        """
        group_input = base_nodes['group_input']
        nodes = base_nodes['nodes']
        links = base_nodes['links']
        
        if mode == "STACKED":
            return group_input.outputs['Geometry']
        elif mode == "COLLECTION":
            # Collection Info node для получения геометрии из коллекции
            collection_info = nodes.new('GeometryNodeCollectionInfo')
            collection_info.location = (-600, 200)
            collection_info.transform_space = 'RELATIVE'
            collection_info.inputs["Separate Children"].default_value = False
            links.new(group_input.outputs['Collection'], collection_info.inputs['Collection'])
            return collection_info.outputs['Instances']
        else:  # OBJECT mode
            # В OBJECT режиме нужен Object Info node для преобразования объекта в геометрию
            object_info = nodes.new('GeometryNodeObjectInfo')
            object_info.location = (-600, 100)
            object_info.transform_space = 'RELATIVE'
            links.new(group_input.outputs['Instance Source'], object_info.inputs['Object'])
            return object_info.outputs['Geometry']
    
    def apply_random_transforms(self, base_nodes, instances_input, index_input):
        """
        Применение случайных трансформаций к инстансам
        
        Args:
            base_nodes: Словарь с базовыми нодами
            instances_input: Входной сокет с инстансами
            index_input: Сокет с индексом для seed
            
        Returns:
            NodeSocket: Выходной сокет с примененными трансформациями
        """
        nodes = base_nodes['nodes']
        links = base_nodes['links']
        group_input = base_nodes['group_input']
        
        current_output = instances_input
        
        # Random Position
        if group_input.outputs.get('Random Position'):
            random_pos = self._create_random_vector_node(
                nodes, links, group_input, index_input, 
                'Random Position', (-200, 400)
            )
            
            set_position = nodes.new('GeometryNodeSetPosition')
            set_position.location = (0, 400)
            links.new(current_output, set_position.inputs['Geometry'])
            links.new(random_pos, set_position.inputs['Offset'])
            current_output = set_position.outputs['Geometry']
        
        # Random Rotation
        if group_input.outputs.get('Random Rotation'):
            random_rot = self._create_random_vector_node(
                nodes, links, group_input, index_input,
                'Random Rotation', (-200, 200)
            )
            
            rotate_instances = nodes.new('GeometryNodeRotateInstances')
            rotate_instances.location = (0, 200)
            links.new(current_output, rotate_instances.inputs['Instances'])
            links.new(random_rot, rotate_instances.inputs['Rotation'])
            current_output = rotate_instances.outputs['Instances']
        
        # Random Scale
        if group_input.outputs.get('Random Scale'):
            random_scale = self._create_random_scale_node(
                nodes, links, group_input, index_input, (-200, 0)
            )
            
            scale_instances = nodes.new('GeometryNodeScaleInstances')
            scale_instances.location = (0, 0)
            links.new(current_output, scale_instances.inputs['Instances'])
            links.new(random_scale, scale_instances.inputs['Scale'])
            current_output = scale_instances.outputs['Instances']
        
        return current_output
    
    def apply_instance_transforms(self, base_nodes, instances_input):
        """
        Применение основных трансформаций к инстансам
        
        Args:
            base_nodes: Словарь с базовыми нодами
            instances_input: Входной сокет с инстансами
            
        Returns:
            NodeSocket: Выходной сокет с примененными трансформациями
        """
        nodes = base_nodes['nodes']
        links = base_nodes['links']
        group_input = base_nodes['group_input']
        
        current_output = instances_input
        
        # Instance Scale
        if group_input.outputs.get('Instance Scale'):
            scale_instances = nodes.new('GeometryNodeScaleInstances')
            scale_instances.location = (200, 300)
            links.new(current_output, scale_instances.inputs['Instances'])
            links.new(group_input.outputs['Instance Scale'], scale_instances.inputs['Scale'])
            current_output = scale_instances.outputs['Instances']
        
        # Instance Rotation
        if group_input.outputs.get('Instance Rotation'):
            rotate_instances = nodes.new('GeometryNodeRotateInstances')
            rotate_instances.location = (200, 100)
            links.new(current_output, rotate_instances.inputs['Instances'])
            links.new(group_input.outputs['Instance Rotation'], rotate_instances.inputs['Rotation'])
            current_output = rotate_instances.outputs['Instances']
        
        return current_output
    
    def apply_global_transforms(self, base_nodes, geometry_input):
        """
        Применение глобальных трансформаций ко всей геометрии
        
        Args:
            base_nodes: Словарь с базовыми нодами  
            geometry_input: Входной сокет с геометрией
            
        Returns:
            NodeSocket: Выходной сокет с примененными трансформациями
        """
        nodes = base_nodes['nodes']
        links = base_nodes['links']
        group_input = base_nodes['group_input']
        
        current_output = geometry_input
        
        # Global Position
        if group_input.outputs.get('Global Position'):
            transform = nodes.new('GeometryNodeTransform')
            transform.location = (600, 200)
            links.new(current_output, transform.inputs['Geometry'])
            links.new(group_input.outputs['Global Position'], transform.inputs['Translation'])
            current_output = transform.outputs['Geometry']
        
        # Global Rotation (добавляем к существующему transform или создаем новый)
        if group_input.outputs.get('Global Rotation'):
            if 'transform' in locals():
                links.new(group_input.outputs['Global Rotation'], transform.inputs['Rotation'])
            else:
                transform = nodes.new('GeometryNodeTransform')
                transform.location = (600, 100)
                links.new(current_output, transform.inputs['Geometry'])
                links.new(group_input.outputs['Global Rotation'], transform.inputs['Rotation'])
                current_output = transform.outputs['Geometry']
        
        return current_output
    
    def _create_random_vector_node(self, nodes, links, group_input, index_input, param_name, location):
        """Создание random vector node с правильным seed"""
        # Random Value node для X
        random_x = nodes.new('FunctionNodeRandomValue')
        random_x.data_type = 'FLOAT'
        random_x.location = (location[0], location[1] + 100)
        
        # Random Value node для Y  
        random_y = nodes.new('FunctionNodeRandomValue')
        random_y.data_type = 'FLOAT'
        random_y.location = (location[0], location[1])
        
        # Random Value node для Z
        random_z = nodes.new('FunctionNodeRandomValue')
        random_z.data_type = 'FLOAT'
        random_z.location = (location[0], location[1] - 100)
        
        # Separate XYZ для получения range значений
        separate_range = nodes.new('ShaderNodeSeparateXYZ')
        separate_range.location = (location[0] - 200, location[1])
        links.new(group_input.outputs[param_name], separate_range.inputs['Vector'])
        
        # Настройка range для каждой оси
        for random_node, component in [(random_x, 'X'), (random_y, 'Y'), (random_z, 'Z')]:
            # Seed = Random Seed + Index + axis offset
            seed_add = nodes.new('ShaderNodeMath')
            seed_add.operation = 'ADD'
            seed_add.location = (location[0] - 100, location[1] + (100 if component == 'X' else 0 if component == 'Y' else -100))
            links.new(group_input.outputs['Random Seed'], seed_add.inputs[0])
            links.new(index_input, seed_add.inputs[1])
            
            # Range: -value to +value
            negate = nodes.new('ShaderNodeMath')
            negate.operation = 'MULTIPLY'
            negate.inputs[1].default_value = -1.0
            negate.location = (location[0] - 150, location[1] + (100 if component == 'X' else 0 if component == 'Y' else -100))
            links.new(separate_range.outputs[component], negate.inputs[0])
            
            links.new(seed_add.outputs[0], random_node.inputs['Seed'])
            links.new(negate.outputs[0], random_node.inputs['Min'])
            links.new(separate_range.outputs[component], random_node.inputs['Max'])
        
        # Combine XYZ
        combine_xyz = nodes.new('ShaderNodeCombineXYZ')
        combine_xyz.location = (location[0] + 200, location[1])
        links.new(random_x.outputs['Value'], combine_xyz.inputs['X'])
        links.new(random_y.outputs['Value'], combine_xyz.inputs['Y'])
        links.new(random_z.outputs['Value'], combine_xyz.inputs['Z'])
        
        return combine_xyz.outputs['Vector']
    
    def _create_random_scale_node(self, nodes, links, group_input, index_input, location):
        """Создание random scale node"""
        random_scale = nodes.new('FunctionNodeRandomValue')
        random_scale.data_type = 'FLOAT'
        random_scale.location = location
        
        # Seed
        seed_add = nodes.new('ShaderNodeMath')
        seed_add.operation = 'ADD'
        seed_add.location = (location[0] - 100, location[1])
        links.new(group_input.outputs['Random Seed'], seed_add.inputs[0])
        links.new(index_input, seed_add.inputs[1])
        
        # Range: 1.0 - Random Scale to 1.0 + Random Scale
        subtract = nodes.new('ShaderNodeMath')
        subtract.operation = 'SUBTRACT'
        subtract.inputs[0].default_value = 1.0
        subtract.location = (location[0] - 150, location[1] + 50)
        links.new(group_input.outputs['Random Scale'], subtract.inputs[1])
        
        add = nodes.new('ShaderNodeMath')
        add.operation = 'ADD'
        add.inputs[0].default_value = 1.0
        add.location = (location[0] - 150, location[1] - 50)
        links.new(group_input.outputs['Random Scale'], add.inputs[1])
        
        links.new(seed_add.outputs[0], random_scale.inputs['Seed'])
        links.new(subtract.outputs[0], random_scale.inputs['Min'])
        links.new(add.outputs[0], random_scale.inputs['Max'])
        
        return random_scale.outputs['Value']
    
    def apply_anti_recursion(self, base_nodes, final_geometry_output, use_anti_recursion=True):
        """
        Применение анти-рекурсии в конце node group - ТОЧНАЯ КОПИЯ ИЗ BACKUP
        
        Args:
            base_nodes: Словарь с базовыми нодами
            final_geometry_output: Финальный выход геометрии перед анти-рекурсией
            use_anti_recursion: Применять ли анти-рекурсию
            
        Returns:
            NodeSocket: Финальный выход для подключения к group_output
        """
        if not use_anti_recursion:
            return final_geometry_output
            
        nodes = base_nodes['nodes']
        links = base_nodes['links']
        group_input = base_nodes['group_input']
        
        # Final Realize Instances (анти-рекурсия) - ТОЧНАЯ КОПИЯ ИЗ BACKUP
        final_realize = nodes.new('GeometryNodeRealizeInstances')
        final_realize.name = "Final Realize Instances"
        final_realize.location = (900, 0)
        links.new(final_geometry_output, final_realize.inputs['Geometry'])
        
        # Final Switch (переключение анти-рекурсии) - ТОЧНАЯ КОПИЯ ИЗ BACKUP
        final_switch = nodes.new('GeometryNodeSwitch')
        final_switch.name = "Final Switch"
        final_switch.input_type = 'GEOMETRY'
        final_switch.location = (1100, 0)
        
        # Подключаем realize instances к switch (ИСПРАВЛЕНО: правильная логика как в backup)
        # True = анти-рекурсия ВЫКЛЮЧЕНА (instances), False = анти-рекурсия ВКЛЮЧЕНА (realized)
        links.new(final_geometry_output, final_switch.inputs['True'])  # Instances
        links.new(final_realize.outputs['Geometry'], final_switch.inputs['False'])  # Realized
        
        # Инверсия для Realize Instances флага (как в advanced_cloners) - ТОЧНАЯ КОПИЯ ИЗ BACKUP
        invert_realize = nodes.new('FunctionNodeBooleanMath')
        invert_realize.name = "Invert Realize Instances"
        invert_realize.operation = 'NOT'
        invert_realize.location = (1000, -100)
        links.new(group_input.outputs['Realize Instances'], invert_realize.inputs[0])
        links.new(invert_realize.outputs[0], final_switch.inputs['Switch'])
        
        return final_switch.outputs['Output']
    
    def create_node_group(self, mode="OBJECT"):
        """
        Базовый метод создания node group с анти-рекурсией для всех клонеров
        
        Args:
            mode: Режим создания клонера
            
        Returns:
            tuple: (node_group, socket_mapping)
        """
        try:
            # Создаем уникальное имя группы
            import time
            base_name = f"{self.bl_idname}Cloner3D"
            unique_suffix = str(int(time.time() * 1000000) % 1000000)
            group_name = f"{base_name}_{unique_suffix}"
            
            # Проверяем уникальность имени
            counter = 1
            original_name = group_name
            while group_name in bpy.data.node_groups:
                group_name = f"{original_name}_{counter}"
                counter += 1
            
            node_group = bpy.data.node_groups.new(name=group_name, type='GeometryNodeTree')
            
            # Создаем интерфейс (базовые + специфичные сокеты)
            specific_sockets = self.get_specific_sockets()
            socket_mapping = self.create_cloner_interface(node_group, specific_sockets)
            
            # Создаем базовые ноды
            base_nodes = self.create_base_nodes(node_group, mode)
            
            # Реализуем логику клонера (переопределяется в дочерних классах)
            final_geometry = self._create_cloner_logic(base_nodes, mode)
            
            # Применяем анти-рекурсию - ВСЕГДА (Switch управляется сокетом)
            anti_recursion_result = self.apply_anti_recursion(base_nodes, final_geometry, use_anti_recursion=True)
            
            # Подключаем к выходу
            base_nodes['links'].new(anti_recursion_result, base_nodes['group_output'].inputs['Geometry'])
            
            print(f"✅ Created {self.bl_idname} Cloner node group: {group_name}")
            return node_group, socket_mapping
            
        except Exception as e:
            print(f"[ERROR] {self.bl_idname} Cloner node group creation failed: {e}")
            import traceback
            traceback.print_exc()
            return None, None
    
    def _create_cloner_logic(self, base_nodes, mode):
        """
        Создание основной логики клонера - ДОЛЖНО БЫТЬ ПЕРЕОПРЕДЕЛЕНО в дочерних классах
        
        Args:
            base_nodes: Словарь с базовыми нодами
            mode: Режим создания клонера
            
        Returns:
            NodeSocket: Финальный выход геометрии перед анти-рекурсией
        """
        raise NotImplementedError("_create_cloner_logic must be implemented in subclasses")
    
    def get_cloner_parameter_groups(self):
        """
        Базовая группировка параметров для UI клонеров
        
        Returns:
            dict: Группы параметров
        """
        return {
            "Instance Transform": ["Instance Scale", "Instance Rotation"],
            "Randomization": ["Random Position", "Random Rotation", "Random Scale", "Random Seed"],
            "Global Transform": ["Global Position", "Global Rotation"],
            "Anti-Recursion": ["Realize Instances"]
        }