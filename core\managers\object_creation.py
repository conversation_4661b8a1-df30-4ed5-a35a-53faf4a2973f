"""
Collection Manager для ClonerPro
Упрощенная версия системы создания коллекций из Advanced Cloners
"""

import bpy


def create_object_copy_for_cloning(context, orig_obj, collection_name=None):
    """
    Создать копию объекта в коллекции для копий - использует пользовательские настройки
    Проверяет существующие копии и создает новую только при необходимости
    """

    # Получить имя коллекции из настроек пользователя
    if collection_name is None:
        from ..system.settings import get_naming_settings
        naming = get_naming_settings()
        collection_name = naming['clone_target_collection']
    
    # 1. Проверить, является ли объект уже клонером или копией
    if is_already_copy_or_cloner(orig_obj, collection_name):
        print(f"Object {orig_obj.name} is already a copy or cloner, returning as-is")
        return orig_obj
    
    # 2. Создать коллекцию ClonerTo если не существует
    cloner_to_collection = ensure_cloner_to_collection(collection_name)
    
    # 3. Проверить, есть ли уже копия для этого объекта
    copy_name = f"{orig_obj.name}_{collection_name}"
    
    # Поиск существующей копии по имени и метаданным
    for obj in cloner_to_collection.objects:
        if obj.name == copy_name or obj.get("original_object") == orig_obj.name:
            print(f"Found existing copy: {obj.name} for {orig_obj.name}")
            return obj
    
    # 4. Создать новую копию только если не найдено
    print(f"Creating new copy for {orig_obj.name}")
    copy_obj = orig_obj.copy()
    if orig_obj.data:
        copy_obj.data = orig_obj.data.copy()
    
    # 5. Установить имя и метаданные
    copy_obj.name = copy_name
    copy_obj["original_object"] = orig_obj.name
    copy_obj["is_cloner_source"] = True
    
    # 6. Добавить в коллекцию ClonerTo
    cloner_to_collection.objects.link(copy_obj)
    
    # 7. Удалить из других коллекций (как в advanced_cloners)
    for collection in copy_obj.users_collection:
        if collection != cloner_to_collection:
            collection.objects.unlink(copy_obj)
    
    # 8. НЕ скрываем автоматически - пользователь сам решает (как в коллекционном режиме)
    copy_obj.hide_viewport = False
    copy_obj.hide_render = False
    
    print(f"Created copy {copy_obj.name} for original {orig_obj.name}")
    return copy_obj


def is_already_copy_or_cloner(orig_obj, collection_name="ClonerTo"):
    """
    Проверить, является ли объект уже клонером или копией - ЛОГИКА ИЗ ADVANCED_CLONERS
    """
    suffix = f"_{collection_name}"
    
    # Проверки из advanced_cloners + unified система
    if (orig_obj.name.startswith("Cloner_") or
        orig_obj.get("original_obj") or  # Это клонер
        orig_obj.name.endswith(suffix) or  # Это копия в ClonerTo
        orig_obj.get("is_cloner_source") or  # Помечен как источник клонера
        # НОВОЕ: Проверки для unified системы
        "Cloner3D_Advanced" in orig_obj.name or  # Grid/Linear клонеры unified системы
        "CollectionCloner" in orig_obj.name or   # Collection клонеры
        orig_obj.name.endswith("Cloner") or      # Любые клонеры
        _has_cloner_modifiers(orig_obj)):        # Есть клонер модификаторы
        return True
    
    return False


def _has_cloner_modifiers(obj):
    """Проверить, есть ли у объекта клонер модификаторы"""
    try:
        for mod in obj.modifiers:
            if mod.type == 'NODES' and mod.node_group:
                # Проверяем метаданные модификатора
                if (mod.get("cloner_type") or 
                    mod.get("is_stacked_cloner") or
                    mod.get("cloner_mode")):
                    return True
                # Проверяем имя node group
                node_group_name = mod.node_group.name
                cloner_patterns = [
                    "GridCloner", "LinearCloner", "CircleCloner", "SpiralCloner",
                    "ObjectCloner", "SplineCloner", "VolumeCloner"
                ]
                for pattern in cloner_patterns:
                    if pattern in node_group_name:
                        return True
        return False
    except:
        return False


def ensure_cloner_to_collection(collection_name="ClonerTo"):
    """
    Убедиться что коллекция ClonerTo существует - КАК В ADVANCED_CLONERS

    При плоской структуре (nested hierarchies отключены) возвращает Scene Collection
    вместо создания новой ClonerTo_ коллекции
    """
    # Проверить настройки nested hierarchies
    from ..system.settings import get_behavior_settings
    behavior = get_behavior_settings()

    # Если плоская структура - использовать Scene Collection
    if not behavior['create_nested_hierarchies']:
        print(f"✅ [FLAT] Using Scene Collection instead of {collection_name} (flat structure)")
        return bpy.context.scene.collection

    # Обычная логика для nested hierarchies
    # Проверить существует ли коллекция
    if collection_name in bpy.data.collections:
        cloner_to_collection = bpy.data.collections[collection_name]
        print(f"Found existing collection {collection_name}")
        return cloner_to_collection

    # Создать коллекцию
    cloner_to_collection = bpy.data.collections.new(collection_name)

    # Добавить в Scene Collection
    bpy.context.scene.collection.children.link(cloner_to_collection)

    # Установить цвет из пользовательских настроек
    from ..system.settings import get_ui_settings
    ui_settings = get_ui_settings()
    collection_color = ui_settings['collection_color']
    if collection_color != 'NONE':
        cloner_to_collection.color_tag = collection_color

    print(f"Created new collection {collection_name} with blue color")
    return cloner_to_collection


def create_cloner_collection(context, orig_obj_or_collection, cloner_type):
    """Создать коллекцию для клонера в структуре CLONERS_ObjectName"""
    
    print(f"🔍 [COLLECTION] Creating collection for {orig_obj_or_collection.name if hasattr(orig_obj_or_collection, 'name') else 'Unknown'}")
    parent_collection = None
    
    # OBJECT MODE: Проверяем, клонируем ли мы из существующего клонера-объекта
    if hasattr(orig_obj_or_collection, 'modifiers'):
        print(f"🔍 [COLLECTION] Checking {len(orig_obj_or_collection.modifiers)} modifiers")
        # Ищем среди модификаторов geometry nodes
        for modifier in orig_obj_or_collection.modifiers:
            if modifier.type == 'NODES':
                print(f"🔍 [COLLECTION] Found NODES modifier: {modifier.name}")
                original_object = modifier.get("original_object")
                cloner_type_found = modifier.get("cloner_type")
                print(f"🔍 [COLLECTION] original_object: {original_object}")
                print(f"🔍 [COLLECTION] cloner_type: {cloner_type_found}")
                
                if original_object:
                    print(f"🔍 [COLLECTION] This is a cloner - searching for parent collection")
                    # Это клонер - ищем его родительскую коллекцию
                    # Получить префикс из пользовательских настроек
                    from ..system.settings import get_naming_settings
                    naming = get_naming_settings()
                    cloner_prefix = naming['cloner_collection']

                    for collection in bpy.data.collections:
                        if collection.name.startswith(cloner_prefix):
                            print(f"🔍 [COLLECTION] Checking CLONERS collection: {collection.name}")
                            # Проверяем все дочерние коллекции этой CLONERS_
                            for child_collection in collection.children:
                                if orig_obj_or_collection.name in child_collection.objects:
                                    print(f"✅ [COLLECTION] Found parent collection: {collection.name}")
                                    parent_collection = collection
                                    break
                            if parent_collection:
                                break
                    break
    
    # COLLECTION MODE: Проверяем, клонируем ли мы из коллекции, созданной клонером
    elif hasattr(orig_obj_or_collection, 'name') and hasattr(orig_obj_or_collection, 'objects'):
        # Это коллекция - проверяем, была ли она создана клонером
        from ...core.core import get_cloner_modifier
        
        # Ищем объекты клонеров, которые могли создать эту коллекцию
        for obj in bpy.data.objects:
            cloner_modifier = get_cloner_modifier(obj)
            if cloner_modifier:
                cloner_collection_name = cloner_modifier.get("cloner_collection", "")
                if cloner_collection_name == orig_obj_or_collection.name:
                    # Найден клонер, который создал эту коллекцию - ищем его родительскую CLONERS_
                    # Получить префикс из пользовательских настроек
                    from ..system.settings import get_naming_settings
                    naming = get_naming_settings()
                    cloner_prefix = naming['cloner_collection']

                    for collection in bpy.data.collections:
                        if collection.name.startswith(cloner_prefix):
                            # Проверяем все дочерние коллекции этой CLONERS_
                            for child_collection in collection.children:
                                if obj.name in child_collection.objects:
                                    parent_collection = collection
                                    break
                            if parent_collection:
                                break
                    break
    
    # Если не найдена существующая коллекция (клонируем из источника), создаём новую
    if not parent_collection:
        # Получить настройки из пользовательских настроек
        from ..system.settings import get_naming_settings, get_behavior_settings
        naming = get_naming_settings()
        behavior = get_behavior_settings()

        # Проверить настройку Nested Hierarchies
        if behavior['create_nested_hierarchies']:
            # Создать вложенную иерархию (как было раньше)
            base_name = naming['cloner_collection'].rstrip('_')  # Убираем _ если есть

            counter = 1
            parent_collection_name = f"{base_name}_{counter:03d}"

            # Убедиться что имя уникально
            while parent_collection_name in bpy.data.collections:
                counter += 1
                parent_collection_name = f"{base_name}_{counter:03d}"

            # Создать родительскую коллекцию
            parent_collection = bpy.data.collections.new(parent_collection_name)
            context.scene.collection.children.link(parent_collection)
            print(f"✅ [COLLECTION] Created nested hierarchy: {parent_collection_name}")
        else:
            # Плоская структура - использовать Scene Collection как родительскую
            parent_collection = context.scene.collection
            print(f"✅ [COLLECTION] Using flat structure: Scene Collection")
    
    # Именование коллекции клонера с пользовательским префиксом
    from ..system.settings import get_naming_settings
    naming = get_naming_settings()
    base_name = naming['cloner_object'].rstrip('_')  # Убираем _ если есть

    counter = 1
    cloner_collection_name = f"{base_name}_{counter:03d}"
    
    # Убедиться что имя уникально
    while cloner_collection_name in bpy.data.collections:
        counter += 1
        cloner_collection_name = f"{base_name}_{counter:03d}"
    
    # Создать коллекцию клонера
    cloner_collection = bpy.data.collections.new(cloner_collection_name)
    
    # Добавить в родительскую коллекцию
    parent_collection.children.link(cloner_collection)
    
    return cloner_collection


def create_cloner_object(orig_obj, cloner_type):
    """Создать объект клонера"""

    # Именование объекта клонера с пользовательским префиксом
    from ..system.settings import get_naming_settings
    naming = get_naming_settings()
    base_name = naming['cloner_object'].rstrip('_')  # Убираем _ если есть

    counter = 1
    cloner_name = f"{base_name}_{counter:03d}"
    
    while cloner_name in bpy.data.objects:
        counter += 1
        cloner_name = f"{base_name}_{counter:03d}"
    
    # Создать mesh и объект
    mesh = bpy.data.meshes.new(f"{cloner_name}_Mesh")
    cloner_obj = bpy.data.objects.new(cloner_name, mesh)
    
    return cloner_obj


def setup_cloner_visibility(cloner_obj):
    """Настроить видимость клонера"""
    # Клонер видим в viewport и рендере
    cloner_obj.hide_viewport = False
    cloner_obj.hide_render = False


def setup_collection_visibility(context, collection):
    """Настроить видимость коллекции"""
    # Коллекция видима в viewport и рендере
    # В Blender нет прямого свойства hide для коллекций,
    # настройки видимости делаются через layer collection
    
    # Найти layer collection
    layer_collection = find_layer_collection(context.view_layer.layer_collection, collection)
    if layer_collection:
        layer_collection.hide_viewport = False


def find_layer_collection(layer_collection, collection):
    """Найти layer collection для коллекции"""
    if layer_collection.collection == collection:
        return layer_collection
    
    for child in layer_collection.children:
        result = find_layer_collection(child, collection)
        if result:
            return result
    
    return None


def create_standard_object_cloner(context, cloner_type, orig_obj, use_anti_recursion=True):
    """
    Создает обычный (не стековый) клонер для объекта - БЕЗ АБСТРАКЦИЙ.
    
    Args:
        context: Контекст Blender
        cloner_type: Тип клонера (GRID, LINEAR, CIRCLE)
        orig_obj: Исходный объект для клонирования
        use_anti_recursion: Использовать анти-рекурсию
    
    Returns:
        bool: True если клонер успешно создан, False в случае ошибки
    """
    try:
        # 1. Создаем копию объекта в коллекции ClonerTo
        cloner_source_obj = create_object_copy_for_cloning(context, orig_obj)
        print(f"Используем объект-источник для клонера: {cloner_source_obj.name}")

        # 2. Создаем объект клонера
        cloner_obj = create_cloner_object(orig_obj, cloner_type)
        if not cloner_obj:
            print("Ошибка при создании объекта клонера")
            return False

        # 3. Создаем коллекцию для клонера
        cloner_collection = create_cloner_collection(context, orig_obj, cloner_type)
        if not cloner_collection:
            print("Ошибка при создании коллекции клонера")
            return False

        # 4. Добавляем клонер-объект в коллекцию
        cloner_collection.objects.link(cloner_obj)

        # 5. Настраиваем видимость
        setup_collection_visibility(context, cloner_collection)
        setup_cloner_visibility(cloner_obj)

        # 6. Создаем модификатор для клонера
        modifier = cloner_obj.modifiers.new(name=f"{cloner_type}Cloner", type='NODES')

        # 7. СОЗДАНИЕ NODE GROUP - для каждого типа клонера
        if cloner_type == "GRID":
            from ...components.cloners.grid import create_main_group
            node_group = create_main_group(f"_{orig_obj.name}", use_anti_recursion)
        elif cloner_type == "LINEAR":
            from ...components.cloners.linear import create_main_group
            node_group = create_main_group(f"_{orig_obj.name}", use_anti_recursion)
        else:
            print(f"Тип клонера {cloner_type} еще не реализован")
            return False

        if not node_group:
            print("Ошибка при создании node group")
            return False

        # 8. Устанавливаем группу узлов для модификатора
        modifier.node_group = node_group

        # 9. КРИТИЧЕСКИ ВАЖНО: Устанавливаем копию объекта в модификаторе
        _set_object_in_modifier(modifier, cloner_source_obj)

        # 10. Устанавливаем метаданные - КРИТИЧЕСКИ ВАЖНО ДЛЯ УДАЛЕНИЯ
        modifier["source_type"] = "OBJECT"
        modifier["cloner_mode"] = "OBJECT"  # Object Mode клонер
        modifier["is_stacked_cloner"] = False  # НЕ стековый
        modifier["original_object"] = orig_obj.name
        modifier["cloner_source_object"] = cloner_source_obj.name
        modifier["cloner_collection"] = cloner_collection.name

        # 11. Делаем клонер-объект активным
        for obj in context.selected_objects:
            obj.select_set(False)
        cloner_obj.select_set(True)
        context.view_layer.objects.active = cloner_obj

        print(f"Grid Cloner успешно создан для объекта {orig_obj.name}")
        return True

    except Exception as e:
        print(f"Error creating object cloner: {e}")
        return False


def _set_object_in_modifier(modifier, orig_obj):
    """
    Устанавливает объект в модификаторе через Object сокет - КРИТИЧЕСКИ ВАЖНО.
    
    Args:
        modifier: Модификатор геометрических узлов
        orig_obj: Объект для установки
    """
    try:
        if not modifier.node_group:
            print(f"WARNING: No node group in modifier")
            return False

        # Ищем Object сокет в интерфейсе группы узлов
        for item in modifier.node_group.interface.items_tree:
            if item.item_type == 'SOCKET' and item.in_out == 'INPUT' and item.name == 'Object':
                socket_id = item.identifier
                try:
                    modifier[socket_id] = orig_obj
                    print(f"Object {orig_obj.name} set in modifier through socket {socket_id}")
                    
                    # Дополнительно устанавливаем объект в узлах ObjectInfo внутри группы
                    _set_object_in_object_info_nodes(modifier.node_group, orig_obj)
                    
                    return True
                except Exception as e:
                    print(f"ERROR: Could not set object in modifier: {e}")
                    return False

        print(f"WARNING: Object socket not found in interface of group {modifier.node_group.name}")
        return False

    except Exception as e:
        print(f"[ERROR] Ошибка при установке объекта в модификаторе: {e}")
        return False


def _set_object_in_object_info_nodes(node_group, orig_obj):
    """
    Устанавливает объект во всех узлах ObjectInfo в группе узлов.
    
    Args:
        node_group: Группа узлов
        orig_obj: Объект для установки
    """
    try:
        # Рекурсивно ищем узлы ObjectInfo во всех группах
        def find_and_set_object_info(group):
            for node in group.nodes:
                if node.bl_idname == 'GeometryNodeObjectInfo':
                    try:
                        node.inputs['Object'].default_value = orig_obj
                        print(f"Object {orig_obj.name} set in ObjectInfo node: {node.name}")
                    except Exception as e:
                        print(f"WARNING: Could not set object in ObjectInfo node {node.name}: {e}")
                elif node.bl_idname == 'GeometryNodeGroup' and node.node_tree:
                    # Рекурсивно обрабатываем вложенные группы
                    find_and_set_object_info(node.node_tree)

        find_and_set_object_info(node_group)

    except Exception as e:
        print(f"ERROR: Error setting object in ObjectInfo nodes: {e}")


def hide_collection(context, collection):
    """Скрыть коллекцию через layer collection"""
    try:
        def find_layer_collection_recursive(layer_collection, target_collection):
            if layer_collection.collection == target_collection:
                return layer_collection
            for child in layer_collection.children:
                result = find_layer_collection_recursive(child, target_collection)
                if result:
                    return result
            return None
        
        layer_collection = find_layer_collection_recursive(context.view_layer.layer_collection, collection)
        if layer_collection:
            layer_collection.hide_viewport = True
            print(f"Hidden collection: {collection.name}")
            return True
        else:
            print(f"Could not find layer collection for: {collection.name}")
            return False
    except Exception as e:
        print(f"Error hiding collection {collection.name}: {e}")
        return False


def create_collection_copy_for_cloning(context, target_collection, collection_name=None):
    """Создать копию коллекции для копий - использует пользовательские настройки"""

    # Получить имя коллекции из настроек пользователя
    if collection_name is None:
        from ..system.settings import get_naming_settings
        naming = get_naming_settings()
        collection_name = naming['clone_target_collection']

    # 1. Проверить, является ли коллекция уже копией
    suffix = f"_{collection_name}"
    # Получить префикс из пользовательских настроек для проверки
    from ..system.settings import get_naming_settings
    naming = get_naming_settings()
    cloner_prefix = naming['cloner_collection']

    if (target_collection.name.startswith(cloner_prefix) or
        target_collection.name.endswith(suffix) or
        target_collection.get("is_cloner_source")):
        print(f"Collection {target_collection.name} is already a cloner copy, returning as-is")
        return target_collection
    
    # 2. Создать коллекцию ClonerTo если не существует
    cloner_to_collection = ensure_cloner_to_collection(collection_name)
    
    # 3. Проверить, есть ли уже копия для этой коллекции
    copy_name = f"{target_collection.name}_{collection_name}"
    
    # Поиск существующей копии по имени
    for collection in cloner_to_collection.children:
        if collection.name == copy_name or collection.get("original_collection") == target_collection.name:
            print(f"Found existing collection copy: {collection.name} for {target_collection.name}")
            return collection
    
    # 4. Создать новую копию коллекции
    print(f"Creating new collection copy for {target_collection.name}")
    collection_copy = bpy.data.collections.new(copy_name)
    collection_copy["original_collection"] = target_collection.name
    collection_copy["is_cloner_source"] = True
    
    # 5. Скопировать все объекты в новую коллекцию
    for obj in target_collection.objects:
        collection_copy.objects.link(obj)
    
    # 6. Рекурсивно копировать дочерние коллекции
    for child_collection in target_collection.children:
        child_copy = create_collection_copy_for_cloning(context, child_collection, collection_name)
        if child_copy and child_copy not in collection_copy.children:
            collection_copy.children.link(child_copy)
    
    # 7. Добавить копию в ClonerTo
    cloner_to_collection.children.link(collection_copy)
    
    print(f"Created collection copy {collection_copy.name} for original {target_collection.name}")
    return collection_copy


def switch_selection_to_cloner_object(cloner_obj, context=None):
    """
    Надежно переключает выбор на объект с клонером
    Перенесено из mesh_creation.py для универсального использования
    
    Args:
        cloner_obj: Объект с модификатором клонера
        context: Blender context (опционально)
    """
    if not cloner_obj:
        print("[SELECTION] ❌ No cloner object provided")
        return False
        
    if context is None:
        context = bpy.context
    
    try:
        # Очищаем текущий выбор
        bpy.ops.object.select_all(action='DESELECT')
        
        # Выделяем cloner объект
        cloner_obj.select_set(True)
        context.view_layer.objects.active = cloner_obj
        
        # Обновляем view layer
        context.view_layer.update()
        
        print(f"[SELECTION] ✅ Successfully switched to: {cloner_obj.name}")
        return True
        
    except Exception as e:
        print(f"[SELECTION] ❌ Error switching selection: {e}")
        return False