"""
Тестовый файл для проверки исправлений в ClonerPro
Проверяет исправления проблем с вложением коллекций, Nested Hierarchies и анти-рекурсией
"""

import bpy
from core.system.settings import get_settings, get_naming_settings, get_behavior_settings
from core.managers.object_creation import create_cloner_collection
from core.templates.cloner_creation import create_cloner_unified


def test_nested_hierarchies():
    """Тест настройки Nested Hierarchies"""
    print("\n=== Тест Nested Hierarchies ===")
    
    settings = get_settings()
    
    # Тест с включенными вложенными иерархиями
    settings.create_nested_hierarchies = True
    print(f"Nested Hierarchies включены: {settings.create_nested_hierarchies}")
    
    # Создать куб для тестирования
    bpy.ops.mesh.primitive_cube_add()
    test_obj = bpy.context.active_object
    test_obj.name = "TestCubeNested"
    
    # Создать коллекцию клонера
    cloner_collection = create_cloner_collection(bpy.context, test_obj, "GRID")
    print(f"Создана коллекция клонера: {cloner_collection.name}")
    
    # Проверить, что создана вложенная структура
    parent_found = False
    for collection in bpy.data.collections:
        if collection.name.startswith("CLONERS_") and cloner_collection.name in [child.name for child in collection.children]:
            parent_found = True
            print(f"✅ Найдена родительская коллекция: {collection.name}")
            break
    
    if not parent_found:
        print("❌ Родительская коллекция не найдена при включенных Nested Hierarchies")
    
    # Тест с выключенными вложенными иерархиями
    settings.create_nested_hierarchies = False
    print(f"Nested Hierarchies выключены: {settings.create_nested_hierarchies}")
    
    # Создать еще один куб
    bpy.ops.mesh.primitive_cube_add()
    test_obj2 = bpy.context.active_object
    test_obj2.name = "TestCubeFlat"
    
    # Создать коллекцию клонера
    cloner_collection2 = create_cloner_collection(bpy.context, test_obj2, "LINEAR")
    print(f"Создана коллекция клонера: {cloner_collection2.name}")
    
    # Проверить, что коллекция создана в Scene Collection
    if cloner_collection2.name in [child.name for child in bpy.context.scene.collection.children]:
        print("✅ Коллекция создана в Scene Collection (плоская структура)")
    else:
        print("❌ Коллекция не найдена в Scene Collection при выключенных Nested Hierarchies")
    
    return True


def test_custom_prefix_collection_nesting():
    """Тест вложения коллекций с пользовательскими префиксами"""
    print("\n=== Тест вложения с пользовательскими префиксами ===")
    
    settings = get_settings()
    
    # Установить пользовательский префикс
    settings.cloner_collection_prefix = "MY_CLONERS_"
    settings.create_nested_hierarchies = True
    
    # Создать первый клонер
    bpy.ops.mesh.primitive_cube_add()
    test_obj1 = bpy.context.active_object
    test_obj1.name = "SourceCube"
    
    cloner1 = create_cloner_unified("GRID", "OBJECT", test_obj1)
    print(f"Создан первый клонер: {cloner1.name if cloner1 else 'None'}")
    
    if cloner1:
        # Создать второй клонер из первого (клонер из клонера)
        cloner2 = create_cloner_unified("LINEAR", "OBJECT", cloner1)
        print(f"Создан второй клонер: {cloner2.name if cloner2 else 'None'}")
        
        if cloner2:
            # Проверить, что оба клонера в одной родительской коллекции
            cloner1_parent = None
            cloner2_parent = None
            
            for collection in bpy.data.collections:
                if collection.name.startswith("MY_CLONERS_"):
                    for child in collection.children:
                        if cloner1.name in child.objects:
                            cloner1_parent = collection
                        if cloner2.name in child.objects:
                            cloner2_parent = collection
            
            if cloner1_parent and cloner2_parent and cloner1_parent == cloner2_parent:
                print(f"✅ Оба клонера в одной родительской коллекции: {cloner1_parent.name}")
            else:
                print(f"❌ Клонеры в разных коллекциях: {cloner1_parent.name if cloner1_parent else 'None'} vs {cloner2_parent.name if cloner2_parent else 'None'}")
    
    return True


def test_anti_recursion_settings():
    """Тест настроек анти-рекурсии"""
    print("\n=== Тест настроек анти-рекурсии ===")
    
    settings = get_settings()
    
    # Тест с включенной анти-рекурсией
    settings.global_anti_recursion = True
    behavior = get_behavior_settings()
    print(f"Глобальная анти-рекурсия включена: {behavior['anti_recursion']}")
    
    # Создать куб для тестирования
    bpy.ops.mesh.primitive_cube_add()
    test_obj = bpy.context.active_object
    test_obj.name = "TestCubeAntiRecursion"
    
    # Создать клонер
    result = create_cloner_unified("GRID", "OBJECT", test_obj)
    
    if result:
        print("✅ Клонер создан с включенной анти-рекурсией")
        
        # Проверить, что у клонера есть модификатор с правильными настройками
        if hasattr(result, 'modifiers'):
            for modifier in result.modifiers:
                if modifier.type == 'NODES' and modifier.node_group:
                    # Проверить, есть ли сокет Realize Instances
                    has_realize_socket = False
                    for item in modifier.node_group.interface.items_tree:
                        if item.name == "Realize Instances":
                            has_realize_socket = True
                            print(f"✅ Найден сокет Realize Instances со значением: {item.default_value}")
                            break
                    
                    if not has_realize_socket:
                        print("❌ Сокет Realize Instances не найден")
    else:
        print("❌ Не удалось создать клонер")
    
    # Тест с выключенной анти-рекурсией
    settings.global_anti_recursion = False
    behavior = get_behavior_settings()
    print(f"Глобальная анти-рекурсия выключена: {behavior['anti_recursion']}")
    
    return True


def test_force_stack_mode():
    """Тест принудительного Stack Mode"""
    print("\n=== Тест принудительного Stack Mode ===")
    
    settings = get_settings()
    
    # Включить принудительный Stack Mode
    settings.force_stack_mode = True
    
    # Создать куб для тестирования
    bpy.ops.mesh.primitive_cube_add()
    test_obj = bpy.context.active_object
    test_obj.name = "TestCubeForceStack"
    
    print(f"Force Stack Mode включен: {settings.force_stack_mode}")
    
    # Попробовать создать клонер в OBJECT режиме - должен переключиться на STACKED
    print("Попытка создать Grid клонер в OBJECT режиме...")
    
    result = create_cloner_unified("GRID", "OBJECT", test_obj)
    
    if result:
        print("✅ Клонер создан успешно")
        if hasattr(result, 'type') and result.type == 'NODES':
            print("✅ Создан модификатор (STACKED режим) вместо объекта")
        else:
            print("⚠️ Создан объект вместо модификатора")
    else:
        print("❌ Не удалось создать клонер")
    
    return True


def run_all_tests():
    """Запустить все тесты исправлений"""
    print("🔧 Запуск тестов исправлений ClonerPro")
    print("=" * 50)
    
    try:
        test_nested_hierarchies()
        test_custom_prefix_collection_nesting()
        test_anti_recursion_settings()
        test_force_stack_mode()
        
        print("\n" + "=" * 50)
        print("✅ Все тесты исправлений пройдены!")
        
    except Exception as e:
        print(f"\n❌ Ошибка в тестах: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    run_all_tests()
