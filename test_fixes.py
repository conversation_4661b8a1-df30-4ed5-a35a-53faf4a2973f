"""
Тестовый файл для проверки исправлений в ClonerPro
Проверяет исправления проблем с вложением коллекций, Nested Hierarchies и анти-рекурсией
"""

import bpy
from core.system.settings import get_settings, get_naming_settings, get_behavior_settings
from core.managers.object_creation import create_cloner_collection, ensure_cloner_to_collection, create_object_copy_for_cloning
from core.templates.cloner_creation import create_cloner_unified


def test_nested_hierarchies():
    """Тест настройки Nested Hierarchies"""
    print("\n=== Тест Nested Hierarchies ===")
    
    settings = get_settings()
    
    # Тест с включенными вложенными иерархиями
    settings.create_nested_hierarchies = True
    print(f"Nested Hierarchies включены: {settings.create_nested_hierarchies}")
    
    # Создать куб для тестирования
    bpy.ops.mesh.primitive_cube_add()
    test_obj = bpy.context.active_object
    test_obj.name = "TestCubeNested"
    
    # Создать коллекцию клонера
    cloner_collection = create_cloner_collection(bpy.context, test_obj, "GRID")
    print(f"Создана коллекция клонера: {cloner_collection.name}")
    
    # Проверить, что создана вложенная структура
    parent_found = False
    for collection in bpy.data.collections:
        if collection.name.startswith("CLONERS_") and cloner_collection.name in [child.name for child in collection.children]:
            parent_found = True
            print(f"✅ Найдена родительская коллекция: {collection.name}")
            break
    
    if not parent_found:
        print("❌ Родительская коллекция не найдена при включенных Nested Hierarchies")
    
    # Тест с выключенными вложенными иерархиями
    settings.create_nested_hierarchies = False
    print(f"Nested Hierarchies выключены: {settings.create_nested_hierarchies}")
    
    # Создать еще один куб
    bpy.ops.mesh.primitive_cube_add()
    test_obj2 = bpy.context.active_object
    test_obj2.name = "TestCubeFlat"
    
    # Создать коллекцию клонера
    cloner_collection2 = create_cloner_collection(bpy.context, test_obj2, "LINEAR")
    print(f"Создана коллекция клонера: {cloner_collection2.name}")
    
    # Проверить, что коллекция создана в Scene Collection
    if cloner_collection2.name in [child.name for child in bpy.context.scene.collection.children]:
        print("✅ Коллекция создана в Scene Collection (плоская структура)")
    else:
        print("❌ Коллекция не найдена в Scene Collection при выключенных Nested Hierarchies")
    
    return True


def test_custom_prefix_collection_nesting():
    """Тест вложения коллекций с пользовательскими префиксами"""
    print("\n=== Тест вложения с пользовательскими префиксами ===")
    
    settings = get_settings()
    
    # Установить пользовательский префикс
    settings.cloner_collection_prefix = "MY_CLONERS_"
    settings.create_nested_hierarchies = True
    
    # Создать первый клонер
    bpy.ops.mesh.primitive_cube_add()
    test_obj1 = bpy.context.active_object
    test_obj1.name = "SourceCube"
    
    cloner1 = create_cloner_unified("GRID", "OBJECT", test_obj1)
    print(f"Создан первый клонер: {cloner1.name if cloner1 else 'None'}")
    
    if cloner1:
        # Создать второй клонер из первого (клонер из клонера)
        cloner2 = create_cloner_unified("LINEAR", "OBJECT", cloner1)
        print(f"Создан второй клонер: {cloner2.name if cloner2 else 'None'}")
        
        if cloner2:
            # Проверить, что оба клонера в одной родительской коллекции
            cloner1_parent = None
            cloner2_parent = None
            
            for collection in bpy.data.collections:
                if collection.name.startswith("MY_CLONERS_"):
                    for child in collection.children:
                        if cloner1.name in child.objects:
                            cloner1_parent = collection
                        if cloner2.name in child.objects:
                            cloner2_parent = collection
            
            if cloner1_parent and cloner2_parent and cloner1_parent == cloner2_parent:
                print(f"✅ Оба клонера в одной родительской коллекции: {cloner1_parent.name}")
            else:
                print(f"❌ Клонеры в разных коллекциях: {cloner1_parent.name if cloner1_parent else 'None'} vs {cloner2_parent.name if cloner2_parent else 'None'}")
    
    return True


def test_anti_recursion_settings():
    """Тест настроек анти-рекурсии"""
    print("\n=== Тест настроек анти-рекурсии ===")
    
    settings = get_settings()
    
    # Тест с включенной анти-рекурсией
    settings.global_anti_recursion = True
    behavior = get_behavior_settings()
    print(f"Глобальная анти-рекурсия включена: {behavior['anti_recursion']}")
    
    # Создать куб для тестирования
    bpy.ops.mesh.primitive_cube_add()
    test_obj = bpy.context.active_object
    test_obj.name = "TestCubeAntiRecursion"
    
    # Создать клонер
    result = create_cloner_unified("GRID", "OBJECT", test_obj)
    
    if result:
        print("✅ Клонер создан с включенной анти-рекурсией")
        
        # Проверить, что у клонера есть модификатор с правильными настройками
        if hasattr(result, 'modifiers'):
            for modifier in result.modifiers:
                if modifier.type == 'NODES' and modifier.node_group:
                    # Проверить, есть ли сокет Realize Instances
                    has_realize_socket = False
                    for item in modifier.node_group.interface.items_tree:
                        if item.name == "Realize Instances":
                            has_realize_socket = True
                            print(f"✅ Найден сокет Realize Instances со значением: {item.default_value}")
                            break
                    
                    if not has_realize_socket:
                        print("❌ Сокет Realize Instances не найден")
    else:
        print("❌ Не удалось создать клонер")
    
    # Тест с выключенной анти-рекурсией
    settings.global_anti_recursion = False
    behavior = get_behavior_settings()
    print(f"Глобальная анти-рекурсия выключена: {behavior['anti_recursion']}")
    
    return True


def test_force_stack_mode_with_nested_hierarchies():
    """Тест принудительного Stack Mode и отключения Nested Hierarchies"""
    print("\n=== Тест Force Stack Mode + Nested Hierarchies ===")

    settings = get_settings()

    # Включить принудительный Stack Mode и Nested Hierarchies
    settings.force_stack_mode = True
    settings.create_nested_hierarchies = True

    # Проверить, что get_behavior_settings() правильно отключает nested hierarchies
    behavior = get_behavior_settings()
    print(f"Force Stack Mode включен: {settings.force_stack_mode}")
    print(f"Nested Hierarchies в настройках: {settings.create_nested_hierarchies}")
    print(f"Nested Hierarchies в поведении: {behavior['create_nested_hierarchies']}")

    if not behavior['create_nested_hierarchies']:
        print("✅ Nested Hierarchies правильно отключены при Force Stack Mode")
    else:
        print("❌ Nested Hierarchies не отключились при Force Stack Mode")

    # Создать куб для тестирования
    bpy.ops.mesh.primitive_cube_add()
    test_obj = bpy.context.active_object
    test_obj.name = "TestCubeForceStack"

    # Попробовать создать клонер в OBJECT режиме - должен переключиться на STACKED
    print("Попытка создать Grid клонер в OBJECT режиме...")

    result = create_cloner_unified("GRID", "OBJECT", test_obj)

    if result:
        print("✅ Клонер создан успешно")
        if hasattr(result, 'type') and result.type == 'NODES':
            print("✅ Создан модификатор (STACKED режим) вместо объекта")
        else:
            print("⚠️ Создан объект вместо модификатора")
    else:
        print("❌ Не удалось создать клонер")

    return True


def test_flat_structure_logic():
    """Тест логики плоской структуры"""
    print("\n=== Тест плоской структуры коллекций ===")

    settings = get_settings()

    # Отключить Force Stack Mode и Nested Hierarchies для плоской структуры
    settings.force_stack_mode = False
    settings.create_nested_hierarchies = False

    behavior = get_behavior_settings()
    print(f"Nested Hierarchies отключены: {not behavior['create_nested_hierarchies']}")

    # Тест 1: Обычные коллекции клонеров (Grid_001, Linear_001)
    bpy.ops.mesh.primitive_cube_add()
    test_obj1 = bpy.context.active_object
    test_obj1.name = "TestCubeFlatStructure"

    cloner_collection = create_cloner_collection(bpy.context, test_obj1, "GRID")
    print(f"Создана коллекция клонера: {cloner_collection.name}")

    # Проверить, что коллекция создана в Scene Collection (плоская структура)
    if cloner_collection.name in [child.name for child in bpy.context.scene.collection.children]:
        print("✅ Коллекция клонера создана в Scene Collection (плоская структура)")
    else:
        print("❌ Коллекция клонера не найдена в Scene Collection при плоской структуре")

    # Проверить, что НЕ создана родительская CLONERS_ коллекция
    cloners_collections = [col for col in bpy.data.collections if col.name.startswith("CLONERS_")]
    if not cloners_collections:
        print("✅ Родительские CLONERS_ коллекции не созданы (плоская структура)")
    else:
        print(f"⚠️ Найдены CLONERS_ коллекции: {[col.name for col in cloners_collections]}")

    # Тест 2: ClonerTo_ коллекции должны НЕ создаваться при плоской структуре
    bpy.ops.mesh.primitive_cube_add()
    test_obj2 = bpy.context.active_object
    test_obj2.name = "TestCubeClonerTo"

    # Попробовать создать ClonerTo_ коллекцию
    cloner_to_collection = ensure_cloner_to_collection(f"ClonerTo_{test_obj2.name}_OBJECT")

    # При плоской структуре должна вернуться Scene Collection
    if cloner_to_collection == bpy.context.scene.collection:
        print("✅ ClonerTo_ коллекция НЕ создана при плоской структуре (используется Scene Collection)")
    else:
        print(f"❌ ClonerTo_ коллекция создана при плоской структуре: {cloner_to_collection.name}")

    # Проверить, что НЕ создана ClonerTo_ коллекция
    cloner_to_collections = [col for col in bpy.data.collections if col.name.startswith("ClonerTo_")]
    if not cloner_to_collections:
        print("✅ ClonerTo_ коллекции не созданы (плоская структура)")
    else:
        print(f"⚠️ Найдены ClonerTo_ коллекции: {[col.name for col in cloner_to_collections]}")

    # Тест 3: Копии объектов должны НЕ создаваться при плоской структуре
    bpy.ops.mesh.primitive_cube_add()
    test_obj3 = bpy.context.active_object
    test_obj3.name = "TestCubeObjectCopy"
    original_objects_count = len(bpy.data.objects)

    # Попробовать создать копию объекта
    copy_obj = create_object_copy_for_cloning(bpy.context, test_obj3)

    # При плоской структуре должен вернуться оригинальный объект
    if copy_obj == test_obj3:
        print("✅ Копия объекта НЕ создана при плоской структуре (используется оригинальный объект)")
    else:
        print(f"❌ Копия объекта создана при плоской структуре: {copy_obj.name}")

    # Проверить, что количество объектов не увеличилось
    new_objects_count = len(bpy.data.objects)
    if new_objects_count == original_objects_count:
        print("✅ Количество объектов не увеличилось (копии не созданы)")
    else:
        print(f"⚠️ Количество объектов увеличилось: {original_objects_count} → {new_objects_count}")

    return True


def test_nested_structure_cloner_to():
    """Тест создания ClonerTo_ коллекций в обычном режиме"""
    print("\n=== Тест ClonerTo_ коллекций в nested режиме ===")

    settings = get_settings()

    # Включить Nested Hierarchies для обычной структуры
    settings.force_stack_mode = False
    settings.create_nested_hierarchies = True

    behavior = get_behavior_settings()
    print(f"Nested Hierarchies включены: {behavior['create_nested_hierarchies']}")

    # Создать куб для тестирования
    bpy.ops.mesh.primitive_cube_add()
    test_obj = bpy.context.active_object
    test_obj.name = "TestCubeNestedClonerTo"

    # Попробовать создать ClonerTo_ коллекцию
    cloner_to_collection = ensure_cloner_to_collection(f"ClonerTo_{test_obj.name}_OBJECT")

    # При nested структуре должна создаться реальная ClonerTo_ коллекция
    if cloner_to_collection != bpy.context.scene.collection:
        print(f"✅ ClonerTo_ коллекция создана при nested структуре: {cloner_to_collection.name}")

        # Проверить, что коллекция добавлена в Scene Collection
        if cloner_to_collection.name in [child.name for child in bpy.context.scene.collection.children]:
            print("✅ ClonerTo_ коллекция правильно добавлена в Scene Collection")
        else:
            print("❌ ClonerTo_ коллекция не найдена в Scene Collection")
    else:
        print("❌ ClonerTo_ коллекция НЕ создана при nested структуре")

    return True


def test_uuid_system_integrity():
    """Тест целостности UUID системы после изменений"""
    print("\n=== Тест целостности UUID системы ===")

    from core.uuid.manager import BlenderClonerUUIDManager

    settings = get_settings()

    # Тест 1: Создание клонера с nested hierarchies
    settings.force_stack_mode = False
    settings.create_nested_hierarchies = True

    bpy.ops.mesh.primitive_cube_add()
    test_obj1 = bpy.context.active_object
    test_obj1.name = "UUIDTestCube1"

    cloner1 = create_cloner_unified("GRID", "OBJECT", test_obj1)

    if cloner1:
        # Проверить UUID метаданные
        modifier1 = None
        for mod in cloner1.modifiers:
            if mod.type == 'NODES':
                modifier1 = mod
                break

        if modifier1:
            cloner_uuid1 = modifier1.get("cloner_uuid")
            chain_uuid1 = modifier1.get("chain_uuid")

            if cloner_uuid1 and chain_uuid1:
                print(f"✅ UUID метаданные созданы: cloner={cloner_uuid1[:8]}..., chain={chain_uuid1[:8]}...")

                # Тест 2: Создание второго клонера из первого (цепочка)
                cloner2 = create_cloner_unified("LINEAR", "OBJECT", cloner1)

                if cloner2:
                    modifier2 = None
                    for mod in cloner2.modifiers:
                        if mod.type == 'NODES':
                            modifier2 = mod
                            break

                    if modifier2:
                        cloner_uuid2 = modifier2.get("cloner_uuid")
                        chain_uuid2 = modifier2.get("chain_uuid")
                        previous_uuid = modifier2.get("previous_cloner_uuid")

                        if chain_uuid1 == chain_uuid2:
                            print("✅ Цепочка клонеров сохраняет chain_uuid")
                        else:
                            print(f"❌ Разные chain_uuid: {chain_uuid1} vs {chain_uuid2}")

                        if previous_uuid == cloner_uuid1:
                            print("✅ Связь previous_cloner_uuid корректна")
                        else:
                            print(f"❌ Неверная связь previous_cloner_uuid: {previous_uuid} vs {cloner_uuid1}")

                        # Тест 3: Сканирование UUID системы
                        scan_result = BlenderClonerUUIDManager.scan_all_cloners_with_uuid()

                        uuid_cloners = len(scan_result['cloners_by_uuid'])
                        chains = len(scan_result['chains_by_uuid'])
                        orphaned = len(scan_result['orphaned_cloners'])

                        print(f"✅ UUID сканирование: {uuid_cloners} клонеров, {chains} цепочек, {orphaned} сирот")

                        if orphaned == 0:
                            print("✅ Нет сломанных связей в UUID системе")
                        else:
                            print(f"⚠️ Найдены сломанные связи: {orphaned}")
                    else:
                        print("❌ Не найден модификатор у второго клонера")
                else:
                    print("❌ Не удалось создать второй клонер")
            else:
                print("❌ UUID метаданные не созданы")
        else:
            print("❌ Не найден модификатор у первого клонера")
    else:
        print("❌ Не удалось создать первый клонер")

    return True


def run_all_tests():
    """Запустить все тесты исправлений"""
    print("🔧 Запуск тестов исправлений ClonerPro")
    print("=" * 50)
    
    try:
        test_nested_hierarchies()
        test_custom_prefix_collection_nesting()
        test_anti_recursion_settings()
        test_force_stack_mode_with_nested_hierarchies()
        test_flat_structure_logic()
        test_nested_structure_cloner_to()
        test_uuid_system_integrity()
        
        print("\n" + "=" * 50)
        print("✅ Все тесты исправлений пройдены!")
        
    except Exception as e:
        print(f"\n❌ Ошибка в тестах: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    run_all_tests()
