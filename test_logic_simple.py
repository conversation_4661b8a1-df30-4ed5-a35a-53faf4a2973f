#!/usr/bin/env python3
"""
Простой тест логики без Blender для проверки настроек
"""

# Мокаем bpy для тестирования
class MockBpy:
    class types:
        class PropertyGroup:
            pass
    
    class props:
        @staticmethod
        def BoolProperty(**kwargs):
            return kwargs.get('default', False)
        
        @staticmethod
        def StringProperty(**kwargs):
            return kwargs.get('default', '')
        
        @staticmethod
        def EnumProperty(**kwargs):
            return kwargs.get('default', kwargs.get('items', [('NONE', 'None', '')])[0][0])

import sys
sys.modules['bpy'] = MockBpy()
sys.modules['bpy.types'] = MockBpy.types
sys.modules['bpy.props'] = MockBpy.props

# Теперь можем импортировать настройки
from core.system.settings import ClonerProSettings, get_behavior_settings

def test_force_stack_mode_logic():
    """Тест логики Force Stack Mode с Nested Hierarchies"""
    print("\n=== Тест логики Force Stack Mode ===")
    
    # Создать настройки
    settings = ClonerProSettings()
    
    # Тест 1: Force Stack Mode OFF, Nested Hierarchies ON
    settings.force_stack_mode = False
    settings.create_nested_hierarchies = True
    
    behavior = get_behavior_settings()
    expected_nested = True
    actual_nested = behavior['create_nested_hierarchies']
    
    print(f"Тест 1 - Force Stack: OFF, Nested: ON")
    print(f"Ожидается nested hierarchies: {expected_nested}")
    print(f"Получено nested hierarchies: {actual_nested}")
    
    if actual_nested == expected_nested:
        print("✅ Тест 1 пройден")
    else:
        print("❌ Тест 1 провален")
    
    # Тест 2: Force Stack Mode ON, Nested Hierarchies ON (должно стать OFF)
    settings.force_stack_mode = True
    settings.create_nested_hierarchies = True
    
    behavior = get_behavior_settings()
    expected_nested = False  # Должно быть отключено из-за Force Stack Mode
    actual_nested = behavior['create_nested_hierarchies']
    
    print(f"\nТест 2 - Force Stack: ON, Nested: ON")
    print(f"Ожидается nested hierarchies: {expected_nested} (отключено Force Stack)")
    print(f"Получено nested hierarchies: {actual_nested}")
    
    if actual_nested == expected_nested:
        print("✅ Тест 2 пройден")
    else:
        print("❌ Тест 2 провален")
    
    # Тест 3: Force Stack Mode OFF, Nested Hierarchies OFF
    settings.force_stack_mode = False
    settings.create_nested_hierarchies = False
    
    behavior = get_behavior_settings()
    expected_nested = False
    actual_nested = behavior['create_nested_hierarchies']
    
    print(f"\nТест 3 - Force Stack: OFF, Nested: OFF")
    print(f"Ожидается nested hierarchies: {expected_nested}")
    print(f"Получено nested hierarchies: {actual_nested}")
    
    if actual_nested == expected_nested:
        print("✅ Тест 3 пройден")
    else:
        print("❌ Тест 3 провален")
    
    return True

def test_settings_integration():
    """Тест интеграции настроек"""
    print("\n=== Тест интеграции настроек ===")
    
    settings = ClonerProSettings()
    
    # Проверить значения по умолчанию
    print(f"Force Stack Mode по умолчанию: {settings.force_stack_mode}")
    print(f"Nested Hierarchies по умолчанию: {settings.create_nested_hierarchies}")
    print(f"Anti-recursion по умолчанию: {settings.use_anti_recursion}")
    
    # Проверить, что get_behavior_settings работает
    behavior = get_behavior_settings()
    print(f"Behavior settings: {behavior}")
    
    if 'create_nested_hierarchies' in behavior:
        print("✅ Behavior settings содержат create_nested_hierarchies")
    else:
        print("❌ Behavior settings НЕ содержат create_nested_hierarchies")
    
    return True

if __name__ == "__main__":
    print("🧪 Запуск простых тестов логики ClonerPro")
    
    try:
        test_settings_integration()
        test_force_stack_mode_logic()
        print("\n✅ Все тесты завершены")
    except Exception as e:
        print(f"\n❌ Ошибка в тестах: {e}")
        import traceback
        traceback.print_exc()
