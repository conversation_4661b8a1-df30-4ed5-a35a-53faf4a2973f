"""
ClonerPro Unified Cloner Creation System
========================================

Объединённая система создания всех типов клонеров во всех режимах.
Сочетает mesh-based и unified creation системы с шаблонами для устранения дублирования кода.

Разделы:
1. 🎯 Main API - Основной интерфейс создания клонеров
2. 🔧 Mesh-Based Cloners - Клонеры работающие с поверхностями (Object, Spline)
3. 🎨 Unified Cloners - Геометрические клонеры (Grid, Linear, Circle)
4. 📋 Template System - Переиспользуемые шаблоны для устранения дублирования
5. 🔗 Helper Functions - Вспомогательные функции
"""

import bpy
from .anti_recursion import (
    apply_anti_recursion_architecture, 
    create_basic_cloner_interface
)
from ..managers.object_creation import (
    ensure_cloner_to_collection, setup_collection_visibility,
    create_cloner_collection, setup_cloner_visibility,
    is_already_copy_or_cloner, create_object_copy_for_cloning,
    create_collection_copy_for_cloning, switch_selection_to_cloner_object
)


# ===============================================
# 🎯 MAIN API - Основной интерфейс
# ===============================================

def create_cloner_unified(cloner_type, mode, source, use_anti_recursion=None, name_suffix="", mesh_stacked=False):
    """
    🎯 ЕДИНАЯ функция создания клонеров для всех типов и режимов
    Автоматически выбирает правильную систему (mesh-based или unified) на основе типа клонера

    Args:
        cloner_type: "GRID", "LINEAR", "CIRCLE", "OBJECT", "SPLINE"
        mode: "OBJECT", "STACKED", "COLLECTION"
        source: source object для OBJECT/STACKED, collection name для COLLECTION
        use_anti_recursion: включить анти-рекурсию (None = использовать настройки пользователя)
        name_suffix: суффикс имени
        mesh_stacked: для mesh клонеров - создавать модификатор прямо на оригинале

    Returns:
        Created cloner object/modifier или None
    """

    # Получить настройки поведения из пользовательских настроек
    from ..system.settings import get_behavior_settings
    behavior = get_behavior_settings()

    # Использовать пользовательские настройки если не указано явно
    if use_anti_recursion is None:
        use_anti_recursion = behavior['anti_recursion']

    # Проверить принудительный Stack Mode
    if behavior['force_stack_mode'] and mode == "OBJECT":
        print(f"🔄 [SETTINGS] Force Stack Mode enabled - switching from OBJECT to STACKED mode")
        mode = "STACKED"
    # Проверяем что клонер зарегистрирован в классовой архитектуре
    from ..registry.cloner_registry import is_cloner_type_registered, create_cloner_instance
    
    if not is_cloner_type_registered(cloner_type):
        print(f"[ERROR] Cloner type {cloner_type} not registered in class-based system")
        return None
    
    cloner_instance = create_cloner_instance(cloner_type)
    if not cloner_instance:
        print(f"[ERROR] Failed to create {cloner_type} cloner instance")
        return None
    
    # Mesh-based клонеры (работают с поверхностями)
    if cloner_type in ["OBJECT", "SPLINE"]:
        print(f"🔧 [MAIN] {cloner_type} Cloner uses mesh-based creation system")
        return _create_mesh_cloner_via_mesh_system(
            cloner_instance, mode, source, use_anti_recursion, name_suffix, mesh_stacked
        )
    
    # Unified клонеры (геометрические)
    elif cloner_type in ["GRID", "LINEAR", "CIRCLE", "SPIRAL"]:
        print(f"🎨 [MAIN] {cloner_type} Cloner uses unified creation system")
        return _create_unified_cloner_via_unified_system(
            cloner_instance, mode, source, use_anti_recursion, name_suffix
        )
    
    else:
        print(f"[ERROR] Unknown cloner type: {cloner_type}")
        return None


def create_mesh_cloner(cloner_type, mode, target_obj=None, collection_name=None, **kwargs):
    """
    🔧 ПРЯМОЙ доступ к mesh-based системе для обратной совместимости
    
    Args:
        cloner_type: "OBJECT", "SPLINE"
        mode: "OBJECT", "STACKED", "COLLECTION"
        target_obj: объект на который клонируем (обязательно для mesh клонеров)
        collection_name: имя коллекции для COLLECTION режима
        **kwargs: дополнительные параметры
    
    Returns:
        Created modifier или объект
    """
    if cloner_type not in MESH_CLONER_REGISTRY:
        print(f"[ERROR] Mesh cloner type {cloner_type} not registered")
        return None
    
    if mode not in ["OBJECT", "STACKED", "COLLECTION"]:
        print(f"[ERROR] Unsupported mode for mesh cloners: {mode}")
        return None
    
    if not target_obj and cloner_type != "SPLINE":
        print(f"[ERROR] Mesh cloners require target_obj")
        return None
    
    print(f"[MESH_CREATION] Creating {cloner_type} mesh cloner in {mode} mode")
    
    # Получаем функцию создания логики
    logic_function = _get_mesh_cloner_logic_function(cloner_type)
    if not logic_function:
        return None
    
    # Создаем в зависимости от режима
    if mode == "OBJECT":
        return _create_object_mesh_cloner(cloner_type, logic_function, target_obj, **kwargs)
    elif mode == "STACKED":
        return _create_stacked_mesh_cloner(cloner_type, logic_function, target_obj, **kwargs)
    elif mode == "COLLECTION":
        return _create_collection_mesh_cloner(cloner_type, logic_function, target_obj, collection_name, **kwargs)
    
    return None


# ===============================================
# 🔧 MESH-BASED CLONERS - Клонеры с поверхностями
# ===============================================

# Registry mesh клонеров
MESH_CLONER_REGISTRY = {
    "OBJECT": "create_object_cloner_logic_group",
    "SPLINE": "create_spline_cloner_logic_group",
}


def _create_mesh_cloner_via_mesh_system(cloner_instance, mode, source, use_anti_recursion, name_suffix, mesh_stacked=False):
    """
    Создание mesh клонеров (Object/Spline) через mesh-based систему
    Использует bridge функции для совместимости
    """
    try:
        import bpy
        context = bpy.context
        cloner_type = cloner_instance.bl_idname
        
        if cloner_type == "OBJECT":
            # Object Cloner logic
            if mode == "STACKED":
                if mesh_stacked:
                    # Mesh Stacked: Модификатор прямо на оригинальном объекте
                    result = _create_direct_object_cloner(source, mode="STACKED")
                    return result
                else:
                    # Обычный STACKED: Используем mesh creation систему (с копией)
                    result = create_mesh_cloner(
                        cloner_type="OBJECT",
                        mode="STACKED",
                        target_obj=source
                    )
                    return result
                
            elif mode == "OBJECT":
                if mesh_stacked:
                    # Mesh Stacked: Модификатор прямо на оригинальном объекте
                    result = _create_direct_object_cloner(source, mode="OBJECT")
                    return result
                else:
                    # Обычный OBJECT: Используем mesh creation систему (с копией)
                    result = create_mesh_cloner(
                        cloner_type="OBJECT",
                        mode="STACKED",  # Внутри mesh система всегда использует STACKED
                        target_obj=source
                    )
                    return result
                
            elif mode == "COLLECTION":
                # COLLECTION: В любом случае нужен Collection UI, но способ создания зависит от mesh_stacked
                if mesh_stacked:
                    # Mesh Stacking: модификатор прямо на оригинальном объекте в Collection режиме
                    active_obj = context.active_object if context else None
                    if not active_obj:
                        print(f"[ERROR] No active object for Object Cloner COLLECTION mode with mesh_stacked")
                        return None
                    result = _create_direct_collection_cloner(active_obj, mode="COLLECTION")
                    return result
                else:
                    # Обычный Collection режим: создаем через mesh creation систему
                    active_obj = context.active_object if context else None
                    if not active_obj:
                        print(f"[ERROR] No active object for Object Cloner COLLECTION mode")
                        return None
                        
                    result = create_mesh_cloner(
                        cloner_type="OBJECT",
                        mode="COLLECTION",
                        target_obj=active_obj
                    )
                    return result
                    
        elif cloner_type == "SPLINE":
            # Spline Cloner logic - работает с кривыми как источником точек
            if mode == "STACKED":
                if mesh_stacked:
                    # Mesh Stacked: Модификатор прямо на оригинальной кривой
                    result = _create_direct_spline_cloner(source, mode="STACKED")
                    return result
                else:
                    # Обычный STACKED: Используем mesh creation систему (с копией)
                    result = create_mesh_cloner(
                        cloner_type="SPLINE",
                        mode="STACKED", 
                        target_obj=source
                    )
                    return result
                
            elif mode == "OBJECT":
                if mesh_stacked:
                    # Mesh Stacked: Модификатор прямо на оригинальной кривой
                    result = _create_direct_spline_cloner(source, mode="OBJECT")
                    return result
                else:
                    # Обычный OBJECT: Используем mesh creation систему (с копией)
                    result = create_mesh_cloner(
                        cloner_type="SPLINE",
                        mode="STACKED",  # Внутри mesh система всегда использует STACKED
                        target_obj=source
                    )
                    return result
                
            elif mode == "COLLECTION":
                if mesh_stacked:
                    # Mesh Stacked: Модификатор прямо на оригинальной кривой в Collection режиме
                    active_obj = context.active_object if context else None
                    if not active_obj:
                        print(f"[ERROR] No active object for Spline Cloner COLLECTION mode with mesh_stacked")
                        return None
                    result = _create_direct_spline_collection_cloner(active_obj, mode="COLLECTION")
                    return result
                else:
                    # Обычный Collection режим: через mesh creation систему
                    active_obj = context.active_object if context else None
                    if not active_obj:
                        print(f"[ERROR] No active object for Spline Cloner COLLECTION mode")
                        return None
                    result = create_mesh_cloner(
                        cloner_type="SPLINE",
                        mode="COLLECTION",
                        target_obj=active_obj
                    )
                    return result
                    
        else:
            print(f"[ERROR] Unknown mesh cloner type: {cloner_type}")
            return None
            
    except Exception as e:
        print(f"❌ [MESH] {cloner_type} Cloner mesh-based creation failed: {e}")
        import traceback
        traceback.print_exc()
        return None


def _get_mesh_cloner_logic_function(cloner_type):
    """Получить функцию создания логики для mesh клонера"""

    if cloner_type == "OBJECT":
        from ...components.cloners.object import object_cloner
        return lambda target_obj=None, use_anti_recursion=True: object_cloner.create_node_group(mode="STACKED", target_obj=target_obj, use_anti_recursion=use_anti_recursion)
    elif cloner_type == "SPLINE":
        from ...components.cloners.spline import spline_cloner
        return lambda target_obj=None, use_anti_recursion=True: spline_cloner.create_node_group(mode="STACKED", target_obj=target_obj, use_anti_recursion=use_anti_recursion)
    
    print(f"[ERROR] Logic function for {cloner_type} not found")
    return None


def _create_object_mesh_cloner(cloner_type, logic_function, target_obj, **kwargs):
    """
    Создает mesh клонер в упрощённом OBJECT режиме
    Создаёт новый объект в коллекции CLONERS_MESH_ с mesh cloner модификатором
    """
    from ..core import get_or_create_cloner_collection, set_cloner_metadata
    
    print(f"[MESH_OBJECT] Creating {cloner_type} object mode cloner on {target_obj.name}")
    
    # 1. Получаем source объект из context
    source_obj = bpy.context.active_object
    if not source_obj or source_obj == target_obj:
        print(f"[ERROR] Need different source object for mesh cloner")
        return None
    
    # 2. Создать коллекцию для mesh клонеров с поддержкой цепочек
    cloner_collection = create_cloner_collection(bpy.context, source_obj, cloner_type)
    
    # 3. Дублировать source объект в коллекцию
    cloner_obj = source_obj.copy()
    cloner_obj.data = source_obj.data.copy()
    cloner_obj.name = f"{source_obj.name}_{cloner_type}_Cloner"
    cloner_collection.objects.link(cloner_obj)
    
    # 4. Добавить mesh cloner модификатор
    use_anti_recursion = kwargs.get('use_anti_recursion', True)
    node_group, socket_mapping = logic_function(target_obj, use_anti_recursion=use_anti_recursion)
    if not node_group:
        print(f"[ERROR] Failed to create node group for {cloner_type}")
        return None
    
    modifier = cloner_obj.modifiers.new(name=f"{cloner_type}Cloner", type='NODES')
    modifier.node_group = node_group
    
    # 5. Настроить Source Object (Original объект) и Target Object
    if "Source Object" in [item.name for item in modifier.node_group.interface.items_tree if item.item_type == 'SOCKET' and item.in_out == 'INPUT']:
        _set_socket_value(modifier, "Source Object", source_obj)
        print(f"✅ [MESH_OBJECT] Set Source Object to Original: {source_obj.name}")
    
    # 6. Стандартные метаданные через core утилиты
    set_cloner_metadata(
        modifier, 
        cloner_type, 
        "OBJECT",
        original_object=source_obj.name,
        target_object=target_obj.name,
        cloner_collection=cloner_collection.name
    )
    
    # 7. Установить параметры по умолчанию
    _setup_mesh_cloner_parameters(modifier, cloner_type, socket_mapping, "OBJECT", **kwargs)
    
    # 8. Переключение выбора на созданный объект
    switch_selection_to_cloner_object(cloner_obj, bpy.context)
    
    print(f"[MESH_OBJECT] ✅ Created {cloner_type} mesh cloner in OBJECT mode")
    return cloner_obj


def _create_stacked_mesh_cloner(cloner_type, logic_function, target_obj, **kwargs):
    """
    Создает mesh клонер в STACKED режиме
    
    Специфика mesh клонеров в STACKED режиме:
    1. Всегда создаем коллекцию ClonerTo с копией объекта
    2. Модификатор вешается на объект в коллекции
    3. НЕ используем wrapper систему
    """
    print(f"[MESH_STACKED] Creating {cloner_type} on {target_obj.name}")
    
    # 1. Создаем коллекцию ClonerTo
    collection_name = f"ClonerTo_{target_obj.name}_{cloner_type}"
    cloner_collection = ensure_cloner_to_collection(collection_name)
    
    # 2. Создаем копию целевого объекта
    cloner_obj = target_obj.copy()
    cloner_obj.data = target_obj.data.copy()
    cloner_obj.name = f"{target_obj.name}_{cloner_type}_Cloner"
    
    # 3. Помещаем в коллекцию
    cloner_collection.objects.link(cloner_obj)
    
    # 4. Создаем node group и модификатор
    node_group, socket_mapping = logic_function(cloner_obj)  # Передаем cloner_obj (копию target_obj)
    
    modifier = cloner_obj.modifiers.new(
        name=f"{cloner_type}_Cloner", 
        type='NODES'
    )
    modifier.node_group = node_group
    
    # 5. Настройка Source Object для STACKED режима
    if cloner_type == "OBJECT":
        # Получаем source объект из контекста (что клонируем)
        source_obj = bpy.context.active_object
        if source_obj and source_obj != target_obj:
            if "Source Object" in [item.name for item in modifier.node_group.interface.items_tree if item.item_type == 'SOCKET' and item.in_out == 'INPUT']:
                _set_socket_value(modifier, "Source Object", source_obj)
                print(f"✅ [MESH_STACKED] Set Source Object to Original: {source_obj.name}")
        else:
            print(f"⚠️ [MESH_STACKED] No valid source object for ObjectCloner")
    
    # 6. Определяем режим клонера
    collection_mode = kwargs.get("collection_mode", False)
    cloner_mode = "COLLECTION" if collection_mode else "STACKED"
    
    # 7. Стандартные метаданные через core утилиты
    from ..core import set_cloner_metadata
    set_cloner_metadata(
        modifier, 
        cloner_type, 
        cloner_mode,
        original_object=target_obj.name,
        target_object=target_obj.name,  # Для mesh клонеров target = source поверхности
        cloner_collection=cloner_collection.name
    )
    
    # 8. Устанавливаем специфичные параметры
    _setup_mesh_cloner_parameters(modifier, cloner_type, socket_mapping, cloner_mode, **kwargs)
    
    # 9. Настройка видимости коллекции
    try:
        setup_collection_visibility(cloner_collection)
    except TypeError:
        # Если функция требует context, попробуем получить его
        setup_collection_visibility(bpy.context, cloner_collection)
    
    # 10. ВАЖНО: Переключаем выбор на созданный объект с модификатором
    switch_selection_to_cloner_object(cloner_obj, bpy.context)
    
    print(f"[MESH_STACKED] ✅ Created {cloner_type} modifier on {cloner_obj.name}")
    return modifier


def _create_collection_mesh_cloner(cloner_type, logic_function, target_obj, collection_name, **kwargs):
    """
    Создает mesh клонер в COLLECTION режиме
    
    Специфика:
    - Target object остается тем же (поверхность для клонирования) 
    - Source = коллекция (что клонируем)
    - UI показывает выбор коллекции вместо объекта
    """
    print(f"[MESH_COLLECTION] Creating {cloner_type} on {target_obj.name} (collection mode)")
    
    # Создаем как в stacked, но передаем collection_mode=True для правильной инициализации
    kwargs["collection_mode"] = True
    modifier = _create_stacked_mesh_cloner(cloner_type, logic_function, target_obj, **kwargs)
    
    if modifier:
        # Source Mode уже установлен правильно в _setup_mesh_cloner_parameters
        
        # Устанавливаем коллекцию если она передана
        if collection_name:
            collection = bpy.data.collections.get(collection_name)
            if collection:
                _set_socket_value(modifier, "Source Collection", collection)
                print(f"[MESH_COLLECTION] ✅ Set collection to {collection_name}")
        
        # Обновляем метаданные (они уже обновлены в _create_stacked_mesh_cloner)
        modifier["cloner_mode"] = "COLLECTION"  # Убеждаемся что режим правильный
        modifier["source_type"] = "COLLECTION"
        
        if collection_name:
            modifier["cloner_collection"] = collection_name
        
        # Принудительно обновляем Geometry Nodes
        from ...ui.utils.ui_helpers import force_geometry_nodes_update
        force_geometry_nodes_update(bpy.context, modifier)
        
        # ВАЖНО: Убеждаемся что выбор остался на cloner объекте (не на оригинальном target)
        cloner_obj = modifier.id_data  # Объект к которому прикреплен модификатор
        if cloner_obj and bpy.context.active_object != cloner_obj:
            switch_selection_to_cloner_object(cloner_obj, bpy.context)
        
        print(f"[MESH_COLLECTION] ✅ Created Collection cloner (collection: {collection_name or 'not set'})")
    
    return modifier


def _setup_mesh_cloner_parameters(modifier, cloner_type, socket_mapping, cloner_mode="STACKED", **kwargs):
    """Устанавливает параметры для mesh клонера"""
    
    # Дефолтные значения для всех mesh клонеров
    defaults = {
        "Distribution Mode": 0,  # Vertices
        "Instance Scale": (1.0, 1.0, 1.0),
        "Instance Rotation": (0.0, 0.0, 0.0),
        "Offset": 0.0,
        "Align to Normal": True,
        "Hide Original": False
    }
    
    # Специфичные дефолты для ObjectCloner
    if cloner_type == "OBJECT":
        # Определяем Source Mode по cloner_mode: COLLECTION = 1, иначе 0
        source_mode = 1 if cloner_mode == "COLLECTION" else 0
        defaults.update({
            "Source Mode": source_mode,
            "Face Center Mode": False,
            "Collection Pick Instance": True if cloner_mode == "COLLECTION" else False,
            "Uniform Scale": False
        })
        print(f"[MESH_SETUP] ObjectCloner Source Mode set to {source_mode} (mode: {cloner_mode})")
    
    # Специфичные дефолты для SplineCloner
    elif cloner_type == "SPLINE":
        # Определяем Source Mode по cloner_mode: COLLECTION = 1, иначе 0
        source_mode = 1 if cloner_mode == "COLLECTION" else 0
        defaults.update({
            "Source Mode": source_mode,
            "Instance Count": 10,
            "Curve Start": 0.0,
            "Curve End": 1.0,
            "Curve Offset": 0.0,
            "Spacing Mode": 0,  # Count
            "Spacing Length": 1.0,
            "Align to Spline": True,
            "Scale Start": (1.0, 1.0, 1.0),
            "Scale End": (1.0, 1.0, 1.0),
            "Rotation Start": (0.0, 0.0, 0.0),
            "Rotation End": (0.0, 0.0, 0.0),
            "Collection Pick Instance": True if cloner_mode == "COLLECTION" else False,
        })
        print(f"[MESH_SETUP] SplineCloner Source Mode set to {source_mode} (mode: {cloner_mode})")
    
    # Устанавливаем дефолтные значения
    for param_name, default_value in defaults.items():
        _set_socket_value(modifier, param_name, default_value)
    
    # Применяем переданные параметры
    for param_name, value in kwargs.items():
        if param_name != "source_mode":  # source_mode уже обработан выше
            _set_socket_value(modifier, param_name, value)


# Direct cloner creation functions (для mesh_stacked режима)
def _create_direct_object_cloner(target_obj, mode="OBJECT"):
    """Создает Object Cloner модификатор прямо на target_obj БЕЗ копирования"""
    from ...components.cloners.object import object_cloner
    
    if not target_obj:
        return None
    
    try:
        # Создаем node group с вычислением edge normals
        use_anti_recursion = kwargs.get('use_anti_recursion', True)
        node_group, socket_mapping = object_cloner.create_node_group(mode="STACKED", target_obj=target_obj, use_anti_recursion=use_anti_recursion)
        if not node_group:
            print(f"[ERROR] Failed to create Object Cloner node group")
            return None
        
        # Добавляем модификатор прямо на target_obj
        modifier = target_obj.modifiers.new(name="ObjectCloner", type='NODES')
        modifier.node_group = node_group
        
        # Устанавливаем метаданные
        modifier["cloner_type"] = "OBJECT"
        modifier["cloner_mode"] = mode
        modifier["is_objectcloner"] = True
        modifier["source_type"] = "OBJECT"
        
        # Настраиваем Source Mode = Object
        if "source_mode" in socket_mapping:
            modifier[socket_mapping["source_mode"]] = 0  # Object mode
        
        # Устанавливаем дефолтные параметры
        _set_direct_cloner_defaults(modifier, socket_mapping, mode="OBJECT")
        
        print(f"✅ [DIRECT] Created Object Cloner modifier directly on {target_obj.name}")
        return modifier
        
    except Exception as e:
        print(f"[ERROR] Failed to create direct Object Cloner modifier: {e}")
        return None


def _create_direct_collection_cloner(target_obj, mode="COLLECTION"):
    """Создает Collection Cloner модификатор прямо на target_obj БЕЗ копирования"""
    from ...components.cloners.object import object_cloner
    
    if not target_obj:
        return None
    
    try:
        # Создаем node group с вычислением edge normals
        use_anti_recursion = kwargs.get('use_anti_recursion', True)
        node_group, socket_mapping = object_cloner.create_node_group(mode="STACKED", target_obj=target_obj, use_anti_recursion=use_anti_recursion)
        if not node_group:
            print(f"[ERROR] Failed to create Collection Cloner node group")
            return None
        
        # Добавляем модификатор прямо на target_obj
        modifier = target_obj.modifiers.new(name="CollectionCloner", type='NODES')
        modifier.node_group = node_group
        
        # Устанавливаем метаданные
        modifier["cloner_type"] = "OBJECT"
        modifier["cloner_mode"] = mode
        modifier["is_objectcloner"] = True
        modifier["source_type"] = "COLLECTION"
        
        # Настраиваем Source Mode = Collection
        if "source_mode" in socket_mapping:
            modifier[socket_mapping["source_mode"]] = 1  # Collection mode
            print(f"✅ [DIRECT] Set Collection mode: socket_mapping['source_mode'] = {socket_mapping['source_mode']}")
        else:
            print(f"❌ [DIRECT] 'source_mode' not found in socket_mapping: {list(socket_mapping.keys())}")
        
        # Устанавливаем дефолтные параметры
        _set_direct_cloner_defaults(modifier, socket_mapping, mode="COLLECTION")
        
        print(f"✅ [DIRECT] Created Collection Cloner modifier directly on {target_obj.name}")
        return modifier
        
    except Exception as e:
        print(f"[ERROR] Failed to create direct Collection Cloner modifier: {e}")
        return None


def _create_direct_spline_cloner(target_obj, mode="OBJECT"):
    """Создает Spline Cloner модификатор прямо на target_obj БЕЗ копирования"""
    from ...components.cloners.spline import spline_cloner
    
    if not target_obj:
        return None
    
    try:
        # Создаем node group для Spline Cloner
        use_anti_recursion = kwargs.get('use_anti_recursion', True)
        node_group, socket_mapping = spline_cloner.create_node_group(mode="STACKED", target_obj=target_obj, use_anti_recursion=use_anti_recursion)
        if not node_group:
            print(f"[ERROR] Failed to create Spline Cloner node group")
            return None
        
        # Добавляем модификатор прямо на target_obj
        modifier = target_obj.modifiers.new(name="SplineCloner", type='NODES')
        modifier.node_group = node_group
        
        # Устанавливаем метаданные
        modifier["cloner_type"] = "SPLINE"
        modifier["cloner_mode"] = mode
        modifier["is_splinecloner"] = True
        modifier["source_type"] = "SPLINE"
        
        # Устанавливаем дефолтные параметры
        _set_direct_cloner_defaults(modifier, socket_mapping, mode="OBJECT")
        
        print(f"✅ [DIRECT] Created Spline Cloner modifier directly on {target_obj.name}")
        return modifier
        
    except Exception as e:
        print(f"[ERROR] Failed to create direct Spline Cloner modifier: {e}")
        return None


def _create_direct_spline_collection_cloner(target_obj, mode="COLLECTION"):
    """Создает Spline Collection Cloner модификатор прямо на target_obj БЕЗ копирования"""
    from ...components.cloners.spline import spline_cloner
    
    if not target_obj:
        return None
    
    try:
        # Создаем node group для Spline Cloner
        use_anti_recursion = kwargs.get('use_anti_recursion', True)
        node_group, socket_mapping = spline_cloner.create_node_group(mode="STACKED", target_obj=target_obj, use_anti_recursion=use_anti_recursion)
        if not node_group:
            print(f"[ERROR] Failed to create Spline Collection Cloner node group")
            return None
        
        # Добавляем модификатор прямо на target_obj
        modifier = target_obj.modifiers.new(name="SplineCollectionCloner", type='NODES')
        modifier.node_group = node_group
        
        # Устанавливаем метаданные
        modifier["cloner_type"] = "SPLINE"
        modifier["cloner_mode"] = mode
        modifier["is_splinecloner"] = True
        modifier["source_type"] = "COLLECTION"
        
        # Настраиваем Collection mode если Spline Cloner поддерживает
        if "source_mode" in socket_mapping:
            modifier[socket_mapping["source_mode"]] = 1  # Collection mode
        
        # Устанавливаем дефолтные параметры
        _set_direct_cloner_defaults(modifier, socket_mapping, mode="COLLECTION")
        
        print(f"✅ [DIRECT] Created Spline Collection Cloner modifier directly on {target_obj.name}")
        return modifier
        
    except Exception as e:
        print(f"[ERROR] Failed to create direct Spline Collection Cloner modifier: {e}")
        return None


def _set_direct_cloner_defaults(modifier, socket_mapping, mode="OBJECT"):
    """Устанавливает дефолтные параметры для direct клонера"""
    
    source_mode = 1 if mode == "COLLECTION" else 0
    
    defaults = {
        "source_mode": source_mode,
        "distribution_mode": 2,  # Faces by default
        "instance_count": 50,
        "density": 1.0,
        "hide_original": False,
        "instance_scale": (1.0, 1.0, 1.0),
        "instance_rotation": (0.0, 0.0, 0.0),
        "random_position": (0.0, 0.0, 0.0),
        "random_rotation": (0.0, 0.0, 0.0),
        "random_scale": 0.0,
        "uniform_scale": False,
        "random_seed": 0,
        "collection_random_seed": 2,
        "offset": 0.0,
        "face_center_mode": False,
        "align_to_normal": False,
        "collection_pick_instance": True,
        "collection_instance_index": 0,
        "collection_object_count": 3
    }
    
    # Устанавливаем параметры через socket mapping
    for param_name, default_value in defaults.items():
        if param_name in socket_mapping:
            modifier[socket_mapping[param_name]] = default_value


# ===============================================
# 🎨 UNIFIED CLONERS - Геометрические клонеры  
# ===============================================

def _create_unified_cloner_via_unified_system(cloner_instance, mode, source, use_anti_recursion, name_suffix):
    """Создание unified клонеров (Grid, Linear, Circle) через unified систему"""
    
    # Стандартная unified логика для геометрических клонеров (GRID, LINEAR, CIRCLE)
    if mode == "OBJECT":
        return _create_object_mode_with_class(cloner_instance, source, use_anti_recursion, name_suffix)
    elif mode == "STACKED":
        return _create_stacked_mode_with_class(cloner_instance, source, use_anti_recursion, name_suffix)
    elif mode == "COLLECTION":
        return _create_collection_mode_with_class(cloner_instance, source, use_anti_recursion, name_suffix)
    else:
        print(f"[ERROR] Unknown mode: {mode}")
        return None


def _create_object_mode_with_class(cloner_instance, source_obj, use_anti_recursion, name_suffix):
    """Создать Object mode cloner с классовой архитектурой"""
    if not source_obj:
        return None
    
    context = bpy.context
    cloner_type = cloner_instance.bl_idname
    
    # Определить правильный источник
    actual_source_obj = source_obj
    if is_already_copy_or_cloner(source_obj):
        for mod in source_obj.modifiers:
            if mod.type == 'NODES' and mod.get("original_object"):
                original_obj_name = mod.get("original_object")
                if original_obj_name and original_obj_name in bpy.data.objects:
                    actual_source_obj = bpy.data.objects[original_obj_name]
                    break
    
    # Создать копию
    cloner_source_obj = create_object_copy_for_cloning(context, actual_source_obj)
    if not cloner_source_obj:
        return None
    
    # Создать коллекцию
    cloner_collection = create_cloner_collection(context, source_obj, cloner_type)
    if not cloner_collection:
        return None
    
    # Создать объект клонера с пользовательским префиксом
    from ..system.settings import get_naming_settings
    naming = get_naming_settings()
    prefix = naming['cloner_object'].rstrip('_')

    base_name = f"{prefix}_{cloner_type}Cloner"
    cloner_name = base_name
    counter = 1
    while cloner_name in bpy.data.objects:
        counter += 1
        cloner_name = f"{base_name}_{counter:03d}"
    
    cloner_mesh = bpy.data.meshes.new(f"{cloner_name}_Mesh")
    cloner_obj = bpy.data.objects.new(cloner_name, cloner_mesh)
    cloner_collection.objects.link(cloner_obj)
    
    # Настроить видимость
    setup_collection_visibility(context, cloner_collection)
    setup_cloner_visibility(cloner_obj)
    
    # Создать node group
    node_group, socket_mapping = cloner_instance.create_node_group(mode="OBJECT", use_anti_recursion=use_anti_recursion)
    if not node_group:
        return None
    
    # Создать модификатор
    modifier = cloner_obj.modifiers.new(name=f"{cloner_type}Cloner", type='NODES')
    modifier.node_group = node_group
    
    # Определяем источник для подключения
    object_to_clone = source_obj if is_already_copy_or_cloner(source_obj) else cloner_source_obj
    
    # UUID
    source_uuid = ""
    try:
        from ...core.chain.detection import _get_or_create_source_uuid
        source_uuid = _get_or_create_source_uuid(object_to_clone)
    except Exception as e:
        print(f"⚠️ [CLASS] Could not create source UUID: {e}")
    
    # Метаданные
    from ...core.core import set_cloner_metadata
    set_cloner_metadata(
        modifier,
        cloner_type,
        "OBJECT",
        original_object=actual_source_obj.name,
        cloner_collection=cloner_collection.name,
        source_type="OBJECT"
    )
    
    if source_uuid:
        modifier["chain_source_uuid"] = source_uuid
    
    # Подключаем объект
    _connect_source_to_modifier(modifier, node_group, object_to_clone, "Object")
    
    # Устанавливаем defaults
    cloner_instance.setup_default_values(modifier, socket_mapping)
    
    # Переключение выбора
    switch_selection_to_cloner_object(cloner_obj, context)
    
    # Chain detection
    try:
        from ...core.chain.detection import setup_cloner_chain_links_only
        setup_cloner_chain_links_only(context, cloner_obj, source_obj, None)
    except Exception as e:
        print(f"⚠️ [CLASS] Chain linking failed: {e}")
    
    print(f"✅ [CLASS] Created {cloner_type} cloner: {cloner_obj.name}")
    return cloner_obj


def _create_stacked_mode_with_class(cloner_instance, target_obj, use_anti_recursion, name_suffix):
    """Создать Stacked mode cloner с классовой архитектурой"""
    if not target_obj:
        return None
    
    cloner_type = cloner_instance.bl_idname
    
    # Уникальное имя модификатора
    base_name = f"{cloner_type}Cloner"
    modifier_name = base_name
    counter = 1
    while modifier_name in [mod.name for mod in target_obj.modifiers]:
        counter += 1
        modifier_name = f"{base_name}_{counter:03d}"
    
    # Создать node group
    node_group, socket_mapping = cloner_instance.create_node_group(mode="STACKED", use_anti_recursion=use_anti_recursion)
    if not node_group:
        return None
    
    # Создать модификатор
    modifier = target_obj.modifiers.new(name=modifier_name, type='NODES')
    modifier.node_group = node_group
    
    # Метаданные
    from ...core.core import set_cloner_metadata
    set_cloner_metadata(
        modifier,
        cloner_type,
        "STACKED",
        original_object=target_obj.name,
        source_type="OBJECT",
        is_stacked_cloner=True
    )
    
    # Defaults
    cloner_instance.setup_default_values(modifier, socket_mapping)
    
    print(f"✅ [CLASS] Created {cloner_type} STACKED cloner: {modifier_name}")
    return modifier


def _create_collection_mode_with_class(cloner_instance, collection_name, use_anti_recursion, name_suffix):
    """Создать Collection mode cloner с классовой архитектурой"""
    if not collection_name or collection_name not in bpy.data.collections:
        return None
    
    target_collection = bpy.data.collections[collection_name]
    context = bpy.context
    cloner_type = cloner_instance.bl_idname
    
    # Определить источник
    from ...core.core import get_cloner_modifier
    actual_source_collection = target_collection
    source_cloner_obj = None
    
    for obj in bpy.data.objects:
        cloner_modifier = get_cloner_modifier(obj)
        if cloner_modifier:
            cloner_collection_name = cloner_modifier.get("cloner_collection", "")
            if cloner_collection_name == target_collection.name:
                source_cloner_obj = obj
                original_collection_name = cloner_modifier.get("original_collection", "")
                if original_collection_name and original_collection_name in bpy.data.collections:
                    actual_source_collection = bpy.data.collections[original_collection_name]
                    break
    
    # Источник для клонирования
    if source_cloner_obj:
        cloner_source_collection = target_collection
    else:
        cloner_source_collection = create_collection_copy_for_cloning(context, actual_source_collection)
    
    if not cloner_source_collection:
        return None
    
    # Создать объект клонера
    base_name = "cloner"
    counter = 1
    cloner_name = f"{base_name}_{counter:03d}"
    while cloner_name in bpy.data.objects:
        counter += 1
        cloner_name = f"{base_name}_{counter:03d}"
    
    cloner_mesh = bpy.data.meshes.new(f"{cloner_name}_Mesh")
    cloner_obj = bpy.data.objects.new(cloner_name, cloner_mesh)
    
    # Создать коллекцию для клонера
    cloner_collection = create_cloner_collection(context, target_collection, cloner_type)
    if not cloner_collection:
        return None
    
    cloner_collection.objects.link(cloner_obj)
    
    # Создать node group
    node_group, socket_mapping = cloner_instance.create_node_group(mode="COLLECTION", use_anti_recursion=use_anti_recursion)
    if not node_group:
        return None
    
    # Создать модификатор
    modifier = cloner_obj.modifiers.new(name=f"{cloner_type}ClonerCollection", type='NODES')
    modifier.node_group = node_group
    
    # UUID
    source_uuid = ""
    try:
        from ...core.chain.detection import _get_or_create_source_uuid
        source_uuid = _get_or_create_source_uuid(cloner_source_collection)
    except Exception as e:
        print(f"⚠️ [CLASS] Could not create collection UUID: {e}")
    
    # Метаданные
    from ...core.core import set_cloner_metadata
    set_cloner_metadata(
        modifier,
        cloner_type,
        "COLLECTION",
        original_collection=actual_source_collection.name,
        cloner_collection=cloner_collection.name,
        source_type="COLLECTION"
    )
    
    if source_uuid:
        modifier["chain_source_uuid"] = source_uuid
    
    # Подключить collection
    _connect_source_to_modifier(modifier, node_group, cloner_source_collection, "Collection")
    
    # Defaults
    cloner_instance.setup_default_values(modifier, socket_mapping)
    
    # Видимость
    setup_collection_visibility(context, cloner_collection)
    setup_cloner_visibility(cloner_obj)
    
    # Переключение выбора
    switch_selection_to_cloner_object(cloner_obj, context)
    
    # Chain detection
    try:
        from ...core.chain.detection import setup_cloner_chain_links_only
        setup_cloner_chain_links_only(context, cloner_obj, source_cloner_obj, actual_source_collection)
    except Exception as e:
        print(f"⚠️ [CLASS] Chain linking failed: {e}")
    
    print(f"✅ [CLASS] Created {cloner_type} COLLECTION cloner: {cloner_obj.name}")
    return cloner_obj


# ===============================================
# 📋 TEMPLATE SYSTEM - Переиспользуемые шаблоны
# ===============================================

def create_base_cloner_logic(cloner_type, name_suffix="", mode="OBJECT", use_anti_recursion=True):
    """
    Create base cloner logic group with common setup
    
    Args:
        cloner_type: Type of cloner (GRID, LINEAR, CIRCLE, etc.)
        name_suffix: Suffix for the node group name
        mode: Mode (OBJECT, STACKED, COLLECTION)
        use_anti_recursion: Whether to include anti-recursion
    
    Returns:
        Tuple of (logic_group, nodes, links, group_input, group_output, sockets)
    """
    # Determine group name based on mode with proper formatting for scanner recognition
    # Convert GRID -> Grid for proper naming
    formatted_type = cloner_type.title()  # GRID -> Grid
    if mode == "STACKED":
        group_name = f"{formatted_type}ClonerStacked{name_suffix}"
    elif mode == "COLLECTION":
        group_name = f"{formatted_type}ClonerCollection{name_suffix}"
    else:
        group_name = f"{formatted_type}ClonerLogic{name_suffix}"
    
    # Create new node group
    logic_group = bpy.data.node_groups.new(type='GeometryNodeTree', name=group_name)
    
    # Create basic interface
    sockets = create_basic_cloner_interface(logic_group, cloner_type)
    
    # Setup nodes
    nodes = logic_group.nodes
    links = logic_group.links
    
    group_input = nodes.new('NodeGroupInput')
    group_input.location = (-800, 0)
    group_output = nodes.new('NodeGroupOutput')
    group_output.location = (800, 0)
    
    return logic_group, nodes, links, group_input, group_output, sockets


def finalize_cloner_with_anti_recursion(nodes, links, group_input, group_output, final_geometry_output, use_anti_recursion=True):
    """
    Finalize cloner by applying anti-recursion architecture
    
    Args:
        nodes, links: Node group nodes and links
        group_input, group_output: Input/output nodes
        final_geometry_output: The final geometry output from cloner logic
        use_anti_recursion: Whether to apply anti-recursion
    """
    apply_anti_recursion_architecture(
        nodes, links, group_input, group_output, 
        final_geometry_output, use_anti_recursion,
        location_offset=(900, 0)
    )


def add_cloner_specific_sockets(node_group, cloner_type):
    """
    Add cloner-specific sockets based on type
    
    Args:
        node_group: The node group to add sockets to
        cloner_type: Type of cloner (GRID, LINEAR, CIRCLE, etc.)
    
    Returns:
        Dictionary with cloner-specific sockets
    """
    specific_sockets = {}
    
    if cloner_type == "GRID":
        # Grid-specific sockets
        count_x = node_group.interface.new_socket(name="Count X", in_out='INPUT', socket_type='NodeSocketInt')
        count_x.default_value = 3
        count_x.min_value = 1
        count_x.max_value = 100
        
        count_y = node_group.interface.new_socket(name="Count Y", in_out='INPUT', socket_type='NodeSocketInt')
        count_y.default_value = 3
        count_y.min_value = 1
        count_y.max_value = 100
        
        count_z = node_group.interface.new_socket(name="Count Z", in_out='INPUT', socket_type='NodeSocketInt')
        count_z.default_value = 1
        count_z.min_value = 1
        count_z.max_value = 100
        
        spacing = node_group.interface.new_socket(name="Spacing", in_out='INPUT', socket_type='NodeSocketVector')
        spacing.default_value = (3.0, 3.0, 3.0)
        
        specific_sockets.update({
            'count_x': count_x,
            'count_y': count_y,
            'count_z': count_z,
            'spacing': spacing
        })
        
    elif cloner_type == "LINEAR":
        # Linear-specific sockets
        count = node_group.interface.new_socket(name="Count", in_out='INPUT', socket_type='NodeSocketInt')
        count.default_value = 5
        count.min_value = 1
        count.max_value = 1000
        
        offset = node_group.interface.new_socket(name="Offset", in_out='INPUT', socket_type='NodeSocketVector')
        offset.default_value = (3.0, 0.0, 0.0)
        
        # Linear-specific gradient parameters
        scale_start = node_group.interface.new_socket(name="Scale Start", in_out='INPUT', socket_type='NodeSocketVector')
        scale_start.default_value = (1.0, 1.0, 1.0)
        
        scale_end = node_group.interface.new_socket(name="Scale End", in_out='INPUT', socket_type='NodeSocketVector')
        scale_end.default_value = (1.0, 1.0, 1.0)
        
        rotation_start = node_group.interface.new_socket(name="Rotation Start", in_out='INPUT', socket_type='NodeSocketVector')
        rotation_start.default_value = (0.0, 0.0, 0.0)
        rotation_start.subtype = 'EULER'
        
        rotation_end = node_group.interface.new_socket(name="Rotation End", in_out='INPUT', socket_type='NodeSocketVector')
        rotation_end.default_value = (0.0, 0.0, 0.0)
        rotation_end.subtype = 'EULER'
        
        specific_sockets.update({
            'count': count,
            'offset': offset,
            'scale_start': scale_start,
            'scale_end': scale_end,
            'rotation_start': rotation_start,
            'rotation_end': rotation_end
        })
        
    elif cloner_type == "CIRCLE":
        # Circle-specific sockets
        count = node_group.interface.new_socket(name="Count", in_out='INPUT', socket_type='NodeSocketInt')
        count.default_value = 8
        count.min_value = 1
        count.max_value = 100
        
        radius = node_group.interface.new_socket(name="Radius", in_out='INPUT', socket_type='NodeSocketFloat')
        radius.default_value = 2.0
        radius.min_value = 0.1
        
        specific_sockets.update({
            'count': count,
            'radius': radius
        })
    
    return specific_sockets


def create_cloner_with_template(cloner_type, cloner_logic_function, name_suffix="", mode="OBJECT", use_anti_recursion=True, **metadata):
    """
    Расширенный template с интеграцией метаданных
    
    Args:
        cloner_type: Type of cloner (GRID, LINEAR, CIRCLE, etc.)
        cloner_logic_function: Function that implements the specific cloner logic
        name_suffix: Suffix for naming
        mode: Mode (OBJECT, STACKED, COLLECTION)
        use_anti_recursion: Whether to apply anti-recursion
        **metadata: Additional metadata for the cloner
    
    Returns:
        The created node group
    """
    try:
        # Create base setup
        logic_group, nodes, links, group_input, group_output, base_sockets = create_base_cloner_logic(
            cloner_type, name_suffix, mode, use_anti_recursion
        )
        
        # Add cloner-specific sockets
        specific_sockets = add_cloner_specific_sockets(logic_group, cloner_type)
        
        # Combine all sockets for easy access
        all_sockets = {**base_sockets, **specific_sockets}
        
        # Call the specific cloner logic function
        final_geometry_output = cloner_logic_function(
            nodes, links, group_input, all_sockets, mode
        )
        
        if not final_geometry_output:
            print(f"[ERROR] Cloner logic function failed for {cloner_type}")
            return None
        
        # ВАЖНО: Анти-рекурсия в logic группе нужна для stacked клонеров
        # Для Object mode wrapper добавит свою анти-рекурсию
        if mode == "STACKED":
            # Для stacked режима применяем анти-рекурсию в logic группе
            finalize_cloner_with_anti_recursion(
                nodes, links, group_input, group_output, 
                final_geometry_output, use_anti_recursion
            )
        else:
            # Для Object/Collection режимов - прямое подключение (wrapper добавит свою)
            links.new(final_geometry_output, group_output.inputs['Geometry'])
        
        # Добавляем метаданные в node group
        _add_node_group_metadata(logic_group, cloner_type, mode, **metadata)
        return logic_group
        
    except Exception as e:
        print(f"[ERROR] Failed to create {cloner_type} cloner: {e}")
        import traceback
        traceback.print_exc()
        return None


def _add_node_group_metadata(node_group, cloner_type, mode, **metadata):
    """
    Добавляет метаданные в node group для обнаружения scanner'ом
    
    Args:
        node_group: Node group для добавления метаданных
        cloner_type: Тип клонера
        mode: Режим работы
        **metadata: Дополнительные метаданные
    """
    # Базовые метаданные
    node_group["cloner_type"] = cloner_type
    node_group["cloner_mode"] = mode
    
    # Системные метаданные из core.py
    from ..core import get_cloner_system, is_unified_cloner, is_mesh_cloner
    node_group["cloner_system"] = get_cloner_system(cloner_type)
    node_group["is_unified_cloner"] = is_unified_cloner(cloner_type)
    node_group["is_mesh_cloner"] = is_mesh_cloner(cloner_type)
    
    # Дополнительные метаданные
    for key, value in metadata.items():
        node_group[key] = value
    
    print(f"✓ Node group metadata set: {cloner_type} ({mode})")


# ===============================================
# 🔗 HELPER FUNCTIONS - Вспомогательные функции
# ===============================================

def _set_socket_value(modifier, socket_name, value):
    """Устанавливает значение сокета модификатора"""
    if not modifier.node_group:
        return False
    
    try:
        for item in modifier.node_group.interface.items_tree:
            if (item.item_type == 'SOCKET' and 
                item.in_out == 'INPUT' and 
                item.name == socket_name):
                modifier[item.identifier] = value
                return True
    except Exception as e:
        print(f"[ERROR] Failed to set socket {socket_name}: {e}")
    
    return False


def _connect_source_to_modifier(modifier, node_group, source, socket_name):
    """Подключить источник к сокету"""
    for socket in node_group.interface.items_tree:
        if socket.item_type == 'SOCKET' and socket.in_out == 'INPUT' and socket.name == socket_name:
            try:
                modifier[socket.identifier] = source
                print(f"✅ [HELPER] Connected {source.name} to {socket_name}")
                return
            except Exception as e:
                print(f"[ERROR] Could not connect {socket_name}: {e}")
    
    # Fallback
    fallback_names = ['Instance Source', 'Source Object'] if socket_name == "Object" else ['Source Collection']
    for fallback_name in fallback_names:
        for socket in node_group.interface.items_tree:
            if socket.item_type == 'SOCKET' and socket.in_out == 'INPUT' and socket.name == fallback_name:
                try:
                    modifier[socket.identifier] = source
                    print(f"✅ [HELPER] Connected {source.name} to {fallback_name} (fallback)")
                    return
                except Exception as e:
                    continue
    
    print(f"⚠️ [HELPER] No {socket_name} socket found")


# Registry functions для обратной совместимости
def get_mesh_cloner_types():
    """Возвращает список доступных mesh клонеров"""
    return list(MESH_CLONER_REGISTRY.keys())


def is_mesh_cloner_type(cloner_type):
    """Проверяет, является ли тип mesh клонером"""
    return cloner_type in MESH_CLONER_REGISTRY


def supports_mesh_cloner_mode(mode):
    """Проверяет, поддерживает ли режим mesh клонеры"""
    return mode in ["STACKED", "COLLECTION"]