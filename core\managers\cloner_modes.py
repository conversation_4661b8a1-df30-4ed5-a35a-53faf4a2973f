"""
UNIFIED Cloner Modes System для ClonerPro
Замена старой системы классов на простую unified функцию
БЕЗ legacy кода, обратной совместимости и мёртвых абстракций

Поддерживает два типа клонеров:
1. Base Cloners (GRID, LINEAR, CIRCLE) - используют unified систему
2. <PERSON><PERSON> Cloners (OBJECT, SPLINE, VOLUME) - используют mesh систему
"""

import bpy


def create_cloner_in_mode(mode, cloner_type, use_anti_recursion=None):
    """
    🎯 УЛУЧШЕННАЯ функция маршрутизации между системами
    Теперь использует явное определение систем из core.py

    Args:
        mode: 'OBJECT', 'STACKED', 'COLLECTION'
        cloner_type: 'GRID', 'LINEAR', 'CIRCLE', 'OBJECT', и т.д.
        use_anti_recursion: включить анти-рекурсию (None = использовать настройки пользователя)
    """
    from ..core import get_cloner_system
    from ..system.settings import get_settings, get_behavior_settings

    context = bpy.context
    settings = get_settings()
    behavior = get_behavior_settings()

    # Использовать пользовательские настройки если не указано явно
    if use_anti_recursion is None:
        use_anti_recursion = behavior['anti_recursion']

    # Проверить принудительный Stack Mode
    if behavior['force_stack_mode'] and mode == 'OBJECT':
        print(f"🔄 [SETTINGS] Force Stack Mode enabled - switching from OBJECT to STACKED mode")
        mode = 'STACKED'
    
    # Creating cloner
    
    # Явная маршрутизация по системам
    system = get_cloner_system(cloner_type)
    
    if system == "unified":
        return _create_unified_cloner_in_mode(mode, cloner_type, use_anti_recursion, context, settings)
    elif system == "mesh":
        return _create_mesh_cloner_in_mode(mode, cloner_type, use_anti_recursion, context, settings)
    else:
        print(f"[ERROR] Unknown cloner type: {cloner_type}")
        return False


def _create_mesh_cloner_in_mode(mode, cloner_type, use_anti_recursion, context, settings):
    """Создание mesh клонера через mesh систему"""
    from ..templates.cloner_creation import create_mesh_cloner, supports_mesh_cloner_mode
    
    # Mesh клонеры не поддерживают OBJECT режим
    if not supports_mesh_cloner_mode(mode):
        print(f"[ERROR] Mesh cloner {cloner_type} does not support {mode} mode")
        return False
    
    # Mesh клонеры всегда требуют target объект
    target_obj = context.active_object
    if not target_obj:
        print(f"[ERROR] Mesh cloner requires active object as target")
        return False
    
    # Дополнительные параметры для mesh клонеров
    kwargs = {"use_anti_recursion": use_anti_recursion}
    
    if mode == 'COLLECTION':
        collection_name = settings.collection_name
        if not collection_name:
            print(f"[ERROR] No collection selected for {mode} mode")
            return False
        
        result = create_mesh_cloner(cloner_type, mode, target_obj, collection_name, **kwargs)
    else:  # STACKED
        # Для ObjectCloner определяем source_mode из настроек
        if cloner_type == "OBJECT":
            source_mode = 0 if settings.creation_mode == 'OBJECT' else 0  # По умолчанию Object mode
            kwargs["source_mode"] = source_mode
        
        result = create_mesh_cloner(cloner_type, mode, target_obj, **kwargs)
    
    if result:
        # Created mesh cloner successfully
        return True
    else:
        print(f"[ERROR] Failed to create mesh {cloner_type} cloner in {mode} mode")
        return False


def _create_unified_cloner_in_mode(mode, cloner_type, use_anti_recursion, context, settings):
    """Создание unified клонера"""
    from ..templates.cloner_creation import create_cloner_unified
    
    # Определяем источник в зависимости от режима для unified клонеров
    if mode in ['OBJECT', 'STACKED']:
        source = context.active_object
        if not source:
            print(f"[ERROR] No active object for {mode} mode")
            return False
    elif mode == 'COLLECTION':
        # ТРЕБУЕМ явного выбора коллекции пользователем (как в mesh клонерах)
        if not settings.collection_name or not settings.collection_name.strip():
            print(f"[ERROR] No collection selected for {mode} mode")
            return False
        
        if settings.collection_name not in bpy.data.collections:
            print(f"[ERROR] Selected collection '{settings.collection_name}' not found")
            return False
        
        source = settings.collection_name
        # Using user-selected collection
    else:
        print(f"[ERROR] Unknown mode: {mode}")
        return False
    
    # Создаём unified клонер
    result = create_cloner_unified(cloner_type, mode, source, use_anti_recursion)
    
    if result:
        # Created unified cloner successfully
        return True
    else:
        print(f"[ERROR] Failed to create unified {cloner_type} cloner in {mode} mode")
        return False


def register():
    """Регистрация unified system - ничего не требуется"""
    pass


def unregister():
    """Отмена регистрации unified system - ничего не требуется"""
    pass