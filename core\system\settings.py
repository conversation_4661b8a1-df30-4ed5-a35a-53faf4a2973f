"""
Простая система регистрации компонентов без DI и сложных абстракций
"""

import bpy
from bpy.types import PropertyGroup
from bpy.props import EnumProperty, StringProperty, BoolProperty, CollectionProperty


# Простые словари для хранения компонентов (вместо Service Container)
CLONERS = {}
EFFECTORS = {}
FIELDS = {}


    
# Свойство для хранения состояний expand/collapse групп в браузере
class BrowserGroupState(PropertyGroup):
    """Состояние группы в браузере"""
    group_name: StringProperty(name="Group Name", default="")
    is_expanded: BoolProperty(name="Is Expanded", default=True)


class ClonerProSettings(PropertyGroup):
    """Расширенные настройки ClonerPro с глобальными параметрами"""

    # ========== ОСНОВНЫЕ НАСТРОЙКИ ==========

    # Режим создания клонеров
    creation_mode: EnumProperty(
        name="Creation Mode",
        description="How to create cloners",
        items=[
            ('OBJECT', 'Object Mode', 'Create new cloner object', 'OBJECT_DATA', 0),
            ('STACKED', 'Stacked Mode', 'Add modifier to original object', 'MODIFIER', 1),
            ('COLLECTION', 'Collection Mode', 'Clone entire collections', 'OUTLINER_COLLECTION', 2),
        ],
        default='OBJECT'
    )

    # Выбранный компонент в браузере
    selected_cloner_type: StringProperty(
        name="Selected Cloner",
        description="Currently selected cloner type",
        default="GRID"
    )

    selected_effector_type: StringProperty(
        name="Selected Effector",
        description="Currently selected effector type",
        default="NOISE"
    )

    selected_field_type: StringProperty(
        name="Selected Field",
        description="Currently selected field type",
        default="SPHERE"
    )

    # Показывать расширенные настройки
    show_advanced: BoolProperty(
        name="Show Advanced",
        description="Show advanced parameters",
        default=False
    )

    # Выбранная коллекция для клонирования
    collection_name: StringProperty(
        name="Collection",
        description="Collection to clone in collection mode",
        default=""
    )

    # Состояния групп в браузере
    browser_group_states: CollectionProperty(type=BrowserGroupState)

    # ========== ГЛОБАЛЬНЫЕ НАСТРОЙКИ ИМЕНОВАНИЯ ==========

    # Префиксы для именования
    cloner_object_prefix: StringProperty(
        name="Cloner Object Prefix",
        description="Prefix for cloner object names",
        default="cloner_"
    )

    cloner_collection_prefix: StringProperty(
        name="Cloner Collection Prefix",
        description="Prefix for parent cloner collections",
        default="CLONERS_"
    )

    clone_target_collection_name: StringProperty(
        name="Clone Target Collection Name",
        description="Name for collections containing cloned objects",
        default="ClonerTo"
    )

    node_group_prefix: StringProperty(
        name="Node Group Prefix",
        description="Prefix for geometry node groups",
        default="ClonerPro_"
    )

    # Настройки автоматического именования
    use_auto_numbering: BoolProperty(
        name="Auto Numbering",
        description="Automatically add numbers to avoid name conflicts",
        default=True
    )

    numbering_format: EnumProperty(
        name="Numbering Format",
        description="Format for automatic numbering",
        items=[
            ('001', '001, 002, 003...', 'Three-digit numbering with leading zeros'),
            ('01', '01, 02, 03...', 'Two-digit numbering with leading zeros'),
            ('1', '1, 2, 3...', 'Simple numbering without leading zeros'),
        ],
        default='001'
    )

    # ========== ГЛОБАЛЬНЫЕ НАСТРОЙКИ ПОВЕДЕНИЯ ==========

    # Системные настройки по умолчанию
    global_anti_recursion: BoolProperty(
        name="Global Anti-Recursion",
        description="Apply anti-recursion to all new cloners by default",
        default=True
    )

    global_auto_update: BoolProperty(
        name="Global Auto Update",
        description="Enable auto-update for all cloners",
        default=True
    )

    global_debug_mode: BoolProperty(
        name="Global Debug Mode",
        description="Enable debug output for troubleshooting",
        default=False
    )

    global_max_instances: IntProperty(
        name="Global Max Instances",
        description="Maximum number of instances per cloner",
        default=10000,
        min=1,
        max=100000
    )

    # Принудительный режим стека
    force_stack_mode: BoolProperty(
        name="Force Stack Mode",
        description="Always create cloners as modifiers on selected object (stack mode)",
        default=False
    )

    # Создание вложенных иерархий
    create_nested_hierarchies: BoolProperty(
        name="Create Nested Hierarchies",
        description="Create nested collection hierarchies for better organization",
        default=True
    )

    # ========== НАСТРОЙКИ КЛОНЕРОВ ПО УМОЛЧАНИЮ ==========

    # Настройки для unified клонеров (Grid, Linear, Circle, Spiral)
    default_unified_count: IntProperty(
        name="Default Count",
        description="Default count for unified cloners",
        default=3,
        min=1,
        max=100
    )

    default_unified_spacing: FloatProperty(
        name="Default Spacing",
        description="Default spacing for unified cloners",
        default=3.0,
        min=0.1,
        max=100.0
    )

    # Настройки для mesh клонеров (Object, Spline)
    default_mesh_instance_count: IntProperty(
        name="Default Instance Count",
        description="Default instance count for mesh cloners",
        default=50,
        min=1,
        max=10000
    )

    default_mesh_density: FloatProperty(
        name="Default Density",
        description="Default density for mesh cloners",
        default=1.0,
        min=0.1,
        max=10.0
    )

    default_mesh_distribution_mode: EnumProperty(
        name="Default Distribution Mode",
        description="Default distribution mode for mesh cloners",
        items=[
            ('0', 'Points', 'Distribute on points'),
            ('1', 'Edges', 'Distribute on edges'),
            ('2', 'Faces', 'Distribute on faces'),
        ],
        default='2'
    )


# Простые функции регистрации (вместо декораторов с метаданными)
def register_cloner(cloner_id, cloner_class):
    """Регистрация клонера"""
    CLONERS[cloner_id] = cloner_class


def register_effector(effector_id, effector_class):
    """Регистрация эффектора"""
    EFFECTORS[effector_id] = effector_class


def register_field(field_id, field_class):
    """Регистрация филда"""
    FIELDS[field_id] = field_class


# Простые функции получения компонентов
def get_cloner(cloner_id):
    """Получить клонер по ID"""
    return CLONERS.get(cloner_id)


def get_effector(effector_id):
    """Получить эффектор по ID"""
    return EFFECTORS.get(effector_id)


def get_field(field_id):
    """Получить филд по ID"""
    return FIELDS.get(field_id)


def get_cloner_list():
    """Получить список всех клонеров"""
    return list(CLONERS.keys())


def get_effector_list():
    """Получить список всех эффекторов"""
    return list(EFFECTORS.keys())


def get_field_list():
    """Получить список всех филдов"""
    return list(FIELDS.keys())


# Функции для работы с настройками
def get_settings():
    """Получить настройки аддона"""
    return bpy.context.scene.clonerpro_settings


def register():
    """Регистрация в Blender"""
    # Безопасная регистрация классов с проверкой
    try:
        bpy.utils.register_class(BrowserGroupState)
    except ValueError as e:
        if "already registered" in str(e):
            print(f"[WARNING] BrowserGroupState already registered, skipping")
        else:
            raise e
    
    try:
        bpy.utils.register_class(ClonerProSettings)
    except ValueError as e:
        if "already registered" in str(e):
            print(f"[WARNING] ClonerProSettings already registered, skipping")
        else:
            raise e
    
    # Проверяем, что свойство не уже установлено
    if not hasattr(bpy.types.Scene, 'clonerpro_settings'):
        bpy.types.Scene.clonerpro_settings = bpy.props.PointerProperty(type=ClonerProSettings)
    
    # Scene properties for stacked and anti-recursion modes (с проверкой на существование)
    if not hasattr(bpy.types.Scene, 'use_stacked_modifiers'):
        bpy.types.Scene.use_stacked_modifiers = BoolProperty(
            default=False,
            name="Use Stacked Modifiers", 
            description="Create all cloners as modifiers on a single object instead of creating a chain of objects. This allows you to easily reorder cloners by moving modifiers up/down. Only works for object cloners."
        )
    
    if not hasattr(bpy.types.Scene, 'use_anti_recursion'):
        bpy.types.Scene.use_anti_recursion = BoolProperty(
            default=True,
            name="Anti-Recursion",
            description="Automatically apply anti-recursion fix to all new cloners. This prevents recursion depth issues when creating chains of cloners."
        )
    
    # Mesh Cloners specific stacking mode
    if not hasattr(bpy.types.Scene, 'use_mesh_stacked_modifiers'):
        bpy.types.Scene.use_mesh_stacked_modifiers = BoolProperty(
            default=False,
            name="Mesh Stacking Mode",
            description="Create mesh cloners (ObjectCloner, etc.) as modifiers on the original object instead of creating copies in ClonerTo collections. This allows you to keep the original object and add multiple mesh cloners as modifiers."
        )
    
    # UI panel expanded states
    if not hasattr(bpy.types.Scene, 'browser_expanded_state'):
        bpy.types.Scene.browser_expanded_state = BoolProperty(
            default=False,
            name="Browser Expanded",
            description="Universal Cloner Browser panel expanded state"
        )
    


def unregister():
    """Отмена регистрации"""
    # Remove scene properties safely
    if hasattr(bpy.types.Scene, "use_stacked_modifiers"):
        del bpy.types.Scene.use_stacked_modifiers
    if hasattr(bpy.types.Scene, "use_anti_recursion"):
        del bpy.types.Scene.use_anti_recursion
    if hasattr(bpy.types.Scene, "use_mesh_stacked_modifiers"):
        del bpy.types.Scene.use_mesh_stacked_modifiers
    if hasattr(bpy.types.Scene, "browser_expanded_state"):
        del bpy.types.Scene.browser_expanded_state
    
    # Безопасное удаление главного свойства
    if hasattr(bpy.types.Scene, "clonerpro_settings"):
        del bpy.types.Scene.clonerpro_settings
    
    # Безопасная отмена регистрации классов
    try:
        bpy.utils.unregister_class(ClonerProSettings)
    except ValueError as e:
        if "not registered" in str(e):
            print(f"[WARNING] ClonerProSettings not registered, skipping")
        else:
            print(f"[ERROR] Failed to unregister ClonerProSettings: {e}")
    
    try:
        bpy.utils.unregister_class(BrowserGroupState)
    except ValueError as e:
        if "not registered" in str(e):
            print(f"[WARNING] BrowserGroupState not registered, skipping")
        else:
            print(f"[ERROR] Failed to unregister BrowserGroupState: {e}")