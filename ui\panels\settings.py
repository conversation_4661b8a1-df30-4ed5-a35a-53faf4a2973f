"""
Settings Panel для ClonerPro
Панель глобальных настроек для пользователей
"""

import bpy
from bpy.types import Panel
from ...core.system.settings import get_settings


class CLONERS_PT_Settings(Panel):
    """Панель глобальных настроек ClonerPro"""
    bl_label = "Settings"
    bl_idname = "CLONERS_PT_Settings"
    bl_space_type = 'VIEW_3D'
    bl_region_type = 'UI'
    bl_category = 'ClonerPro'
    bl_order = 10  # Показывать в конце после основных панелей
    bl_options = {'DEFAULT_CLOSED'}  # Панель свернута по умолчанию

    def draw(self, context):
        layout = self.layout
        settings = get_settings()

        # Секция 1: Настройки именования
        self.draw_naming_settings(layout, settings)

        # Секция 2: Настройки поведения
        self.draw_behavior_settings(layout, settings)

    def draw_naming_settings(self, layout, settings):
        """Секция настроек именования"""
        # Сворачиваемая секция
        naming_box = layout.box()
        naming_header = naming_box.row()
        naming_header.prop(settings, "show_naming_settings",
                          icon="TRIA_DOWN" if settings.show_naming_settings else "TRIA_RIGHT",
                          icon_only=True, emboss=False)
        naming_header.label(text="Naming", icon='OUTLINER')

        if settings.show_naming_settings:
            # Префикс коллекций клонеров
            col = naming_box.column(align=True)
            col.prop(settings, "cloner_collection_prefix", text="Collection Prefix")

            # Имя коллекций для копий
            col.prop(settings, "clone_target_collection_name", text="Target Collection")

            # Префикс объектов клонеров
            col.prop(settings, "cloner_object_prefix", text="Object Prefix")

    def draw_behavior_settings(self, layout, settings):
        """Секция настроек поведения"""
        # Сворачиваемая секция
        behavior_box = layout.box()
        behavior_header = behavior_box.row()
        behavior_header.prop(settings, "show_behavior_settings",
                            icon="TRIA_DOWN" if settings.show_behavior_settings else "TRIA_RIGHT",
                            icon_only=True, emboss=False)
        behavior_header.label(text="Behavior", icon='SETTINGS')

        if settings.show_behavior_settings:
            col = behavior_box.column(align=True)

            # Принудительный Stack Mode
            col.prop(settings, "force_stack_mode", text="Force Stack Mode")

            # Плоская структура - отключаем если Force Stack Mode включен
            flat_row = col.row()
            flat_row.enabled = not settings.force_stack_mode  # Отключаем если stacked
            flat_row.prop(settings, "use_flat_structure", text="Flat Structure")

            # Автоматическая анти-рекурсия
            col.prop(settings, "global_anti_recursion", text="Auto Anti-Recursion")




def register():
    """Register settings panel"""
    bpy.utils.register_class(CLONERS_PT_Settings)


def unregister():
    """Unregister settings panel"""
    bpy.utils.unregister_class(CLONERS_PT_Settings)
