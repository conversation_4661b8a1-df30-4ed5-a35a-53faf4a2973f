"""
Settings Panel для ClonerPro
Панель глобальных настроек для пользователей
"""

import bpy
from bpy.types import Panel
from ...core.system.settings import get_settings


class CLONERS_PT_Settings(Panel):
    """Панель глобальных настроек ClonerPro"""
    bl_label = "Settings"
    bl_idname = "CLONERS_PT_Settings"
    bl_space_type = 'VIEW_3D'
    bl_region_type = 'UI'
    bl_category = 'ClonerPro'
    bl_order = 10  # Показывать в конце после основных панелей

    def draw(self, context):
        layout = self.layout
        settings = get_settings()
        
        # Секция 1: Настройки именования
        self.draw_naming_settings(layout, settings)
        
        # Секция 2: Настройки поведения
        self.draw_behavior_settings(layout, settings)
        
        # Секция 3: Настройки UI
        self.draw_ui_settings(layout, settings)

    def draw_naming_settings(self, layout, settings):
        """Секция настроек именования"""
        naming_box = layout.box()
        naming_box.label(text="Naming", icon='OUTLINER')
        
        # Префикс коллекций клонеров
        col = naming_box.column(align=True)
        col.prop(settings, "cloner_collection_prefix", text="Collection Prefix")
        
        # Имя коллекций для копий
        col.prop(settings, "clone_target_collection_name", text="Target Collection")
        
        # Префикс объектов клонеров
        col.prop(settings, "cloner_object_prefix", text="Object Prefix")

    def draw_behavior_settings(self, layout, settings):
        """Секция настроек поведения"""
        behavior_box = layout.box()
        behavior_box.label(text="Behavior", icon='SETTINGS')
        
        col = behavior_box.column(align=True)
        
        # Принудительный Stack Mode
        col.prop(settings, "force_stack_mode", text="Force Stack Mode")

        # Создание вложенных иерархий - отключаем если Force Stack Mode включен
        nested_row = col.row()
        nested_row.enabled = not settings.force_stack_mode  # Отключаем если stacked
        nested_row.prop(settings, "create_nested_hierarchies", text="Nested Hierarchies")

        # Подсказка если отключено
        if settings.force_stack_mode:
            info_row = col.row()
            info_row.label(text="Nested Hierarchies disabled in Stack Mode", icon="INFO")

        # Автоматическая анти-рекурсия
        col.prop(settings, "global_anti_recursion", text="Auto Anti-Recursion")

    def draw_ui_settings(self, layout, settings):
        """Секция настроек UI"""
        ui_box = layout.box()
        ui_box.label(text="Interface", icon='COLOR')
        
        col = ui_box.column(align=True)
        
        # Цвет коллекций
        col.prop(settings, "collection_color", text="Collection Color")


def register():
    """Register settings panel"""
    bpy.utils.register_class(CLONERS_PT_Settings)


def unregister():
    """Unregister settings panel"""
    bpy.utils.unregister_class(CLONERS_PT_Settings)
