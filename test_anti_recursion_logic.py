"""
Тест логики анти-рекурсии для проверки исправлений
"""

import bpy

def test_anti_recursion_logic():
    """Тестирует логику анти-рекурсии для разных типов клонеров"""
    
    print("=== ТЕСТ ЛОГИКИ АНТИ-РЕКУРСИИ ===")
    
    # Импортируем необходимые модули
    from core.managers.cloner_modes import create_cloner_in_mode
    from core.system.settings import get_settings, get_behavior_settings
    
    # Получаем настройки
    settings = get_settings()
    behavior = get_behavior_settings()
    
    print(f"Глобальная настройка Auto Anti-Recursion: {behavior['anti_recursion']}")
    print(f"Настройка сцены Anti-Recursion: {bpy.context.scene.use_anti_recursion}")
    
    # Создаем тестовый объект
    bpy.ops.mesh.primitive_cube_add()
    test_obj = bpy.context.active_object
    test_obj.name = "TestCube"
    
    print("\n--- ТЕСТ UNIFIED КЛОНЕРОВ ---")
    
    # Тест Grid клонера (unified)
    print("Создание Grid клонера...")
    try:
        result = create_cloner_in_mode("OBJECT", "GRID")
        if result:
            print("✅ Grid клонер создан успешно")
            # Проверяем значение анти-рекурсии в модификаторе
            if hasattr(bpy.context.active_object, 'modifiers') and bpy.context.active_object.modifiers:
                modifier = bpy.context.active_object.modifiers[-1]
                if modifier.type == 'NODES' and modifier.node_group:
                    for input in modifier.node_group.interface.items_tree:
                        if input.name == "Realize Instances":
                            print(f"   Realize Instances default: {input.default_value}")
                            break
        else:
            print("❌ Grid клонер не создан")
    except Exception as e:
        print(f"❌ Ошибка создания Grid клонера: {e}")
    
    print("\n--- ТЕСТ MESH КЛОНЕРОВ ---")
    
    # Создаем второй объект для mesh клонера
    bpy.ops.mesh.primitive_plane_add()
    surface_obj = bpy.context.active_object
    surface_obj.name = "TestSurface"
    
    # Выбираем исходный объект
    bpy.context.view_layer.objects.active = test_obj
    
    # Тест Object клонера (mesh)
    print("Создание Object клонера...")
    try:
        result = create_cloner_in_mode("STACKED", "OBJECT")
        if result:
            print("✅ Object клонер создан успешно")
            # Проверяем значение анти-рекурсии в модификаторе
            if hasattr(surface_obj, 'modifiers') and surface_obj.modifiers:
                modifier = surface_obj.modifiers[-1]
                if modifier.type == 'NODES' and modifier.node_group:
                    for input in modifier.node_group.interface.items_tree:
                        if input.name == "Realize Instances":
                            print(f"   Realize Instances default: {input.default_value}")
                            break
        else:
            print("❌ Object клонер не создан")
    except Exception as e:
        print(f"❌ Ошибка создания Object клонера: {e}")
    
    print("\n=== ТЕСТ ЗАВЕРШЕН ===")

if __name__ == "__main__":
    test_anti_recursion_logic()
